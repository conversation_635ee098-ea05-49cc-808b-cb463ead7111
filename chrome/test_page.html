<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test strony iGabinet - Harmonogram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .schedule-table th,
        .schedule-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .schedule-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .appointment {
            background-color: #e3f2fd;
            margin: 2px;
            padding: 4px;
            border-radius: 4px;
            font-size: 12px;
        }

        .patient-name {
            font-weight: bold;
            color: #1976d2;
        }

        .appointment-time {
            color: #666;
            font-size: 11px;
        }

        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="instructions">
            <h3>🧪 Strona testowa dla dodatku KtoOstatni</h3>
            <p><strong>Instrukcje:</strong></p>
            <ol>
                <li>Ta strona symuluje harmonogram iGabinet</li>
                <li>Otwórz dodatek Chrome "KtoOstatni"</li>
                <li>Wprowadź kod synchronizacji: <code>igab000000000001</code></li>
                <li>Kliknij "🚀 Synchronizuj wizyty"</li>
                <li>Dodatek powinien wyciągnąć dane z tej strony</li>
            </ol>
        </div>

        <div class="header">
            <h1>Harmonogram wizyt</h1>
            <h2>Piątek, 16 sierpnia 2025</h2>
        </div>

        <table class="schedule-table">
            <thead>
                <tr>
                    <th>Godzina</th>
                    <th class="header__column-cell" data-doctor-id="10">ginekolog dr n.med. Małgorzata
                        Olesiak-Andryszczak</th>
                    <th class="header__column-cell" data-doctor-id="105">ginekolog dr Beata Dawiec</th>
                    <th class="header__column-cell" data-doctor-id="114">ginekolog dr Ewelina Sądaj</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>09:00</td>
                    <td>
                        <div class="work-schedule__term appointment" data-id="igab_appointment_001"
                            title="PESEL: ***********, Tel: +***********, Email: <EMAIL>">
                            <div class="term-content__hours appointment-time">09:00 - 09:30</div>
                            <div class="term-content__user patient-name">Anna Kowalska</div>
                            <div class="term-content__service">Konsultacja kardiologiczna</div>
                        </div>
                    </td>
                    <td></td>
                    <td>
                        <div class="work-schedule__term appointment" data-id="igab_appointment_002"
                            title="PESEL: ***********, Tel: +***********, Email: <EMAIL>">
                            <div class="term-content__hours appointment-time">09:00 - 09:30</div>
                            <div class="term-content__user patient-name">Maria Nowak</div>
                            <div class="term-content__service">Badanie USG</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>09:30</td>
                    <td></td>
                    <td>
                        <div class="work-schedule__term appointment" data-id="igab_appointment_003"
                            title="PESEL: ***********, Tel: +***********, Email: <EMAIL>">
                            <div class="term-content__hours appointment-time">09:30 - 10:00</div>
                            <div class="term-content__user patient-name">Katarzyna Wiśniewska</div>
                            <div class="term-content__service">Kontrola</div>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>10:00</td>
                    <td>
                        <div class="work-schedule__term appointment" data-id="igab_appointment_004"
                            title="PESEL: ***********, Tel: +***********, Email: <EMAIL>">
                            <div class="term-content__hours appointment-time">10:00 - 10:30</div>
                            <div class="term-content__user patient-name">Joanna Testowa</div>
                            <div class="term-content__service">Wizyta kontrolna</div>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </tbody>
        </table>

        <div style="margin-top: 30px; padding: 15px; background-color: #e8f5e8; border-radius: 5px;">
            <h4>📊 Oczekiwane rezultaty:</h4>
            <ul>
                <li><strong>3 lekarzy:</strong> ginekolog dr n.med. Małgorzata Olesiak-Andryszczak (ID: igab_10),
                    ginekolog dr Beata Dawiec (ID: igab_105), ginekolog dr Ewelina Sądaj (ID: igab_114)</li>
                <li><strong>4 wizyty:</strong> Anna Kowalska, Maria Nowak, Katarzyna Wiśniewska, Joanna Testowa</li>
                <li><strong>Dane pacjentek:</strong> PESEL, telefon, email w atrybucie title</li>
                <li><strong>Rzeczywiste ID lekarzy:</strong> Pobrane z API iGabinet getAvailableProducts</li>
                <li><strong>Synchronizacja:</strong> Dane powinny zostać wysłane do systemu KtoOstatni z prawidłowymi ID
                </li>
            </ul>

            <div style="margin-top: 20px; text-align: center;">
                <button onclick="testMapping()"
                    style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                    🧪 Test mapowania lekarzy
                </button>
                <button onclick="testExtraction()"
                    style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                    📊 Test wyciągania danych
                </button>
                <button onclick="testExtensionCommunication()"
                    style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                    🔗 Test komunikacji z rozszerzeniem
                </button>
            </div>
        </div>
    </div>

    <script>
        // Symulacja środowiska iGabinet
        console.log('KtoOstatni: Strona testowa iGabinet załadowana');

        // Dodaj event listener dla debugowania
        document.addEventListener('DOMContentLoaded', function () {
            console.log('KtoOstatni: DOM załadowany, gotowy do testowania dodatku');
            console.log('KtoOstatni: Znalezione nagłówki lekarzy:', document.querySelectorAll('.header__column-cell').length);
            console.log('KtoOstatni: Znalezione wizyty:', document.querySelectorAll('.work-schedule__term').length);

            // Dodaj informację o załadowaniu content script
            setTimeout(() => {
                console.log('KtoOstatni: Sprawdzam czy content script jest załadowany...');
                if (typeof extractScheduleFromPage === 'function') {
                    console.log('KtoOstatni: Content script załadowany - funkcja extractScheduleFromPage dostępna');
                } else {
                    console.log('KtoOstatni: Content script NIE załadowany - funkcja extractScheduleFromPage niedostępna');
                }
            }, 1000);
        });

        // Dodaj funkcję testową
        window.testExtraction = function () {
            console.log('KtoOstatni: Test ręcznego wyciągania danych...');
            if (typeof extractScheduleFromPage === 'function') {
                extractScheduleFromPage()
                    .then(data => {
                        console.log('KtoOstatni: Dane wyciągnięte:', data);
                        return data;
                    })
                    .catch(error => {
                        console.error('KtoOstatni: Błąd wyciągania:', error);
                        return null;
                    });
            } else {
                console.error('KtoOstatni: Funkcja extractScheduleFromPage niedostępna');
                return null;
            }
        };

        // Dodaj funkcję testową mapowania
        window.testMapping = function () {
            console.log('KtoOstatni: Test mapowania lekarzy...');
            if (typeof mapDoctorNameToId === 'function' && typeof fetchDoctorsFromIGabinet === 'function') {
                fetchDoctorsFromIGabinet()
                    .then(doctors => {
                        console.log('KtoOstatni: Lekarze z API:', doctors);

                        const testNames = [
                            'ginekolog dr n.med. Małgorzata Olesiak-Andryszczak',
                            'ginekolog dr Beata Dawiec',
                            'ginekolog dr Ewelina Sądaj'
                        ];

                        testNames.forEach(name => {
                            const id = mapDoctorNameToId(name, doctors);
                            console.log(`Mapowanie: "${name}" -> ID: ${id}`);
                        });
                    })
                    .catch(error => {
                        console.error('KtoOstatni: Błąd pobierania lekarzy:', error);
                    });
            } else {
                console.error('KtoOstatni: Funkcje mapowania niedostępne');
            }
        };

        // Dodaj funkcję testową komunikacji z rozszerzeniem
        window.testExtensionCommunication = function () {
            console.log('KtoOstatni: Test komunikacji z rozszerzeniem...');

            // Sprawdź czy chrome.runtime jest dostępny
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                console.error('KtoOstatni: Chrome runtime niedostępny - rozszerzenie nie jest załadowane');
                return;
            }

            // Test ping do content script (samego siebie)
            console.log('KtoOstatni: Wysyłam ping do content script...');
            chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('KtoOstatni: Błąd ping:', chrome.runtime.lastError.message);
                } else {
                    console.log('KtoOstatni: Ping response:', response);
                }
            });

            // Test komunikacji z background script
            console.log('KtoOstatni: Wysyłam test do background script...');
            chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('KtoOstatni: Błąd komunikacji z background:', chrome.runtime.lastError.message);
                } else {
                    console.log('KtoOstatni: Background response:', response);
                }
            });
        };
    </script>
</body>

</html>