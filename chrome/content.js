// Content script dla <PERSON><PERSON>ni - Synchronizacja wizyt z iGabinet (API Version)
console.log('KtoOstatni: Content script zała<PERSON>wany na:', window.location.href);
console.log('KtoOstatni: Chrome runtime dostępny:', typeof chrome !== 'undefined' && !!chrome.runtime);

// Sprawdź czy jesteśmy na stronie iGabinet
const isIGabinet = window.location.href.includes('igabinet.pl');
const isTestPage = window.location.href.includes('test_page.html');

console.log('KtoOstatni: isIGabinet:', isIGabinet, 'isTestPage:', isTestPage);

if (isIGabinet || isTestPage) {
    console.log('KtoOstatni: Jesteśmy na odpowiedniej stronie');

    // Wyślij sygnał gotowości do background script
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
            action: 'contentScriptReady',
            url: window.location.href,
            timestamp: new Date().toISOString()
        }).then(() => {
            console.log('KtoOstatni: Sygnał gotowości wysłany pomyślnie');
        }).catch(error => {
            console.log('KtoOstatni: Nie można wysłać sygnału gotowości:', error);
        });
    } else {
        console.error('KtoOstatni: Chrome runtime niedostępny!');
    }
} else {
    console.log('KtoOstatni: Nie jesteśmy na odpowiedniej stronie, content script nieaktywny');
}

// Nasłuchuj na wiadomości z popup i background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('KtoOstatni: Content script otrzymał wiadomość:', request);

    if (request.action === 'ping') {
        sendResponse({
            success: true,
            message: 'Content script jest aktywny',
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        return true;
    }

    if (request.action === 'extractScheduleData') {
        // Pobierz kod synchronizacji z żądania lub z konfiguracji
        const syncCode = request.syncCode || null;
        console.log('KtoOstatni: Otrzymano żądanie extractScheduleData z kodem:', syncCode);

        extractScheduleFromPage(syncCode)
            .then(data => {
                sendResponse({
                    success: true,
                    data: data,
                    timestamp: new Date().toISOString()
                });
            })
            .catch(error => {
                console.error('KtoOstatni: Błąd wyciągania danych:', error);
                sendResponse({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            });
        return true; // Asynchroniczna odpowiedź
    }

    return false;
});

// Globalna cache dla słownika lekarzy
let doctorsDictionary = null;

// Funkcja do pobierania słownika wszystkich lekarzy (wykonywana raz)
async function fetchDoctorsDictionary() {
    if (doctorsDictionary) {
        console.log('KtoOstatni: Używam cache słownika lekarzy');
        return doctorsDictionary;
    }

    try {
        console.log('KtoOstatni: Pobieranie słownika lekarzy z API getAvailableProducts');

        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, używam danych testowych');
            doctorsDictionary = [
                { "id": 10, "name": "ginekolog dr n.med. Małgorzata Olesiak-Andryszczak", "deleted": "0" },
                { "id": 20, "name": "ginekolog dr Natalia Kubat", "deleted": "0" },
                { "id": 72, "name": "ginekolog dr Joanna Nestorowicz-Czernianin", "deleted": "0" },
                { "id": 105, "name": "ginekolog dr Beata Dawiec", "deleted": "0" },
                { "id": 114, "name": "ginekolog dr Ewelina Sądaj", "deleted": "0" },
                { "id": 129, "name": "ginekolog dr Jakub Andrzejewski", "deleted": "0" },
                { "id": 1748423446, "name": "Gabinet Położnej", "deleted": "0" },
                // Dodatkowi lekarze dla testów
                { "id": 200, "name": "ginekolog dr Oliwia Kopera", "deleted": "0" },
                { "id": 201, "name": "ginekolog dr Aneta Walaszek-Gruszka", "deleted": "0" },
                { "id": 202, "name": "ginekolog dr Yuliia Baraniak", "deleted": "0" },
                { "id": 203, "name": "ginekolog dr Tomasz Kościelniak", "deleted": "0" },
                { "id": 204, "name": "ginekolog dr Agnieszka Tyszko-Tymińska", "deleted": "0" }
            ];
            return doctorsDictionary;
        }

        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getAvailableProducts"
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania słownika lekarzy:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getAvailableProducts:', data);

        // Parsuj odpowiedź
        let doctors = [];
        if (data.success && data.data && Array.isArray(data.data)) {
            doctors = data.data;
        } else if (Array.isArray(data)) {
            doctors = data;
        } else {
            console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi słownika lekarzy:', data);
            return [];
        }

        // Zapisz do cache
        doctorsDictionary = doctors;
        console.log('KtoOstatni: Słownik lekarzy zapisany do cache:', doctors.length, 'lekarzy');
        return doctors;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania słownika lekarzy:', error);
        return [];
    }
}

// Funkcja do pobierania ID lekarzy pracujących w danym dniu
async function fetchWorkingDoctorIds(date) {
    try {
        const formattedDate = date.toISOString().split('T')[0];
        console.log('KtoOstatni: Pobieranie lekarzy pracujących w dniu:', formattedDate);

        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, zwracam testowe ID');
            return [10, 105, 114];
        }

        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getWorkingProducts",
                "date": formattedDate,
                "facility_id": [1]
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania pracujących lekarzy:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getWorkingProducts:', data);

        // Parsuj odpowiedź - getWorkingProducts zwraca tablicę ID lekarzy
        let doctorIds = [];
        if (data.success && data.data && Array.isArray(data.data)) {
            doctorIds = data.data;
        } else if (Array.isArray(data)) {
            doctorIds = data;
        } else {
            console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi getWorkingProducts:', data);
            return [];
        }

        console.log('KtoOstatni: ID lekarzy pracujących w dniu', formattedDate, ':', doctorIds);
        return doctorIds;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania pracujących lekarzy:', error);
        return [];
    }
}

// Funkcja do wyciągania danych harmonogramu z API iGabinet
async function extractScheduleFromPage(syncCode = null) {
    console.log('KtoOstatni: Rozpoczynam pobieranie danych z API iGabinet...');
    console.log('KtoOstatni: Aktualny URL:', window.location.href);
    console.log('KtoOstatni: Kod synchronizacji:', syncCode);

    try {
        // Sprawdź kolejne dni robocze w poszukiwaniu wizyt
        const scheduleData = await findScheduleWithAppointmentsFromAPI(syncCode);

        if (scheduleData) {
            console.log('KtoOstatni: Znaleziono dane harmonogramu z API:', scheduleData);
            return scheduleData;
        }

        throw new Error('Nie znaleziono wizyt w kolejnych dniach roboczych');

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania danych z API:', error);
        throw error;
    }
}

// Funkcja do sprawdzania kolejnych dni roboczych w poszukiwaniu wizyt z API
async function findScheduleWithAppointmentsFromAPI(syncCode = null) {
    console.log('KtoOstatni: Sprawdzam kolejne dni robocze w poszukiwaniu wizyt z API...');
    console.log('KtoOstatni: Używam kodu synchronizacji:', syncCode);

    // 1. Najpierw pobierz słownik wszystkich lekarzy (raz)
    const doctorsDictionary = await fetchDoctorsDictionary();
    if (!doctorsDictionary || doctorsDictionary.length === 0) {
        console.error('KtoOstatni: Nie udało się pobrać słownika lekarzy');
        return null;
    }

    const today = new Date();
    const maxDaysToCheck = 5; // Sprawdź maksymalnie 5 dni do przodu

    for (let dayOffset = 0; dayOffset < maxDaysToCheck; dayOffset++) {
        const checkDate = new Date(today);
        checkDate.setDate(today.getDate() + dayOffset);

        // Pomiń weekendy (sobota = 6, niedziela = 0)
        if (checkDate.getDay() === 0 || checkDate.getDay() === 6) {
            console.log(`KtoOstatni: Pomijam weekend: ${checkDate.toLocaleDateString('pl-PL')}`);
            continue;
        }

        console.log(`KtoOstatni: Sprawdzam dzień ${dayOffset}: ${checkDate.toLocaleDateString('pl-PL')}`);

        try {
            // 2. Pobierz ID lekarzy pracujących w tym dniu
            console.log(`KtoOstatni: Pobieram ID lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}`);
            const workingDoctorIds = await fetchWorkingDoctorIds(checkDate);
            console.log(`KtoOstatni: Znaleziono ${workingDoctorIds.length} lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}:`, workingDoctorIds);

            if (!workingDoctorIds || workingDoctorIds.length === 0) {
                console.warn(`KtoOstatni: Brak lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}`);
                continue;
            }

            // 3. Pobierz wizyty z API dla tego dnia i pracujących lekarzy
            const scheduleData = await fetchScheduleFromAPI(doctorsDictionary, workingDoctorIds, checkDate, syncCode);

            if (scheduleData) {
                console.log(`KtoOstatni: Znaleziono dane dla dnia ${checkDate.toLocaleDateString('pl-PL')}`);
                return scheduleData; // Zwracamy dane nawet jeśli nie ma wizyt
            } else {
                console.log(`KtoOstatni: Brak danych dla dnia ${checkDate.toLocaleDateString('pl-PL')}`);
            }
        } catch (error) {
            console.warn(`KtoOstatni: Błąd sprawdzania dnia ${checkDate.toLocaleDateString('pl-PL')}:`, error);
        }
    }

    console.log('KtoOstatni: Nie znaleziono wizyt w kolejnych dniach roboczych');
    return null;
}

// Funkcja do pobierania wizyt z API iGabinet dla konkretnej daty
async function fetchScheduleFromAPI(doctorsDictionary, workingDoctorIds, date, syncCode = null) {
    console.log(`KtoOstatni: Pobieranie wizyt z API dla daty: ${date.toISOString()}`);
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length, 'lekarzy');
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);

    try {
        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, używam danych testowych');
            return createTestScheduleData(doctorsDictionary, workingDoctorIds, date, syncCode);
        }

        // Format YYYY-MM-DD dla API
        const formattedDate = date.toISOString().split('T')[0];

        console.log(`KtoOstatni: Pobieranie wizyt dla daty: ${formattedDate}`);
        console.log('KtoOstatni: Wywołuję API getEntities z body:', JSON.stringify({
            "section": "getEntities",
            "start_date": formattedDate,
            "end_date": formattedDate,
            "products": workingDoctorIds
        }));

        // Wywołaj API getEntities
        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getEntities",
                "start_date": formattedDate,
                "end_date": formattedDate,
                "products": workingDoctorIds
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania wizyt:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getEntities:', data);

        // Przetwórz dane z API na format systemu KtoOstatni
        return processAPIScheduleData(data, doctorsDictionary, workingDoctorIds, date, syncCode);

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania wizyt z API:', error);
        throw error;
    }
}

// Funkcja do przetwarzania danych z API na format systemu KtoOstatni
function processAPIScheduleData(apiData, doctorsDictionary, workingDoctorIds, date, syncCode = null) {
    console.log('KtoOstatni: Przetwarzam dane z API...');
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length);
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);

    const doctors = [];
    const doctorMap = new Map();

    // Stwórz mapę WSZYSTKICH lekarzy ze słownika (nie tylko pracujących)
    console.log('KtoOstatni: Tworzę mapę WSZYSTKICH lekarzy ze słownika');
    doctorsDictionary.forEach(doctorFromDictionary => {
        const id = parseInt(doctorFromDictionary.id);
        if (!isNaN(id)) {
            const doctorName = doctorFromDictionary.name || `Lekarz ID ${id}`;

            doctorMap.set(id, {
                id: id,
                name: doctorName,
                deleted: doctorFromDictionary.deleted || "0"
            });

            doctors.push({
                doctorId: `${id}`,
                doctorName: doctorName,
                appointments: []
            });

            console.log(`KtoOstatni: Dodano lekarza ze słownika: ID ${id} - ${doctorName}`);
        }
    });

    // Dodaj lekarzy pracujących, którzy nie są w słowniku (fallback)
    workingDoctorIds.forEach(doctorId => {
        const id = parseInt(doctorId);
        if (!isNaN(id) && !doctorMap.has(id)) {
            console.warn(`KtoOstatni: Lekarz ID ${id} pracuje ale nie ma go w słowniku - dodaję z domyślną nazwą`);
            const defaultName = `Lekarz ID ${id}`;

            doctorMap.set(id, {
                id: id,
                name: defaultName,
                deleted: "0"
            });

            doctors.push({
                doctorId: `${id}`,
                doctorName: defaultName,
                appointments: []
            });
        }
    });

    console.log('KtoOstatni: Mapa lekarzy:', Array.from(doctorMap.entries()));
    let totalAppointments = 0;

    // Przetwórz wizyty z API
    // Struktura odpowiedzi API: { success: true, data: { visits: [...], reservations: [...], notes: [...] } }
    if (apiData.success && apiData.data) {
        const apiDataContent = apiData.data;

        // Przetwórz wizyty (visits)
        if (Array.isArray(apiDataContent.visits)) {
            console.log(`KtoOstatni: Znaleziono ${apiDataContent.visits.length} wizyt w odpowiedzi API`);

            apiDataContent.visits.forEach((visit, index) => {
                try {
                    console.log(`KtoOstatni: Przetwarzam wizytę ${index + 1}:`, {
                        id: visit.id,
                        product_id: visit.product?.id,
                        patient: visit.user?.name,
                        start: visit.start_date,
                        end: visit.end_date
                    });

                    // Użyj product.id z wizyty
                    const productId = visit.product?.id;
                    if (!productId) {
                        console.warn('KtoOstatni: Wizyta nie ma product.id:', visit);
                        return;
                    }

                    // Znajdź lekarza w mapie
                    const doctor = doctorMap.get(parseInt(productId));
                    if (!doctor) {
                        console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w mapie lekarzy`);
                        return;
                    }

                    // Przetwórz wizytę
                    const processedAppointment = processVisitData(visit);
                    if (processedAppointment) {
                        // Znajdź lekarza w tablicy doctors
                        const doctorIndex = doctors.findIndex(d => parseInt(d.doctorId) === parseInt(productId));
                        if (doctorIndex !== -1) {
                            doctors[doctorIndex].appointments.push(processedAppointment);
                            totalAppointments++;
                            console.log(`KtoOstatni: Dodano wizytę do lekarza ${doctor.name}`);
                        } else {
                            console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w tablicy doctors`);
                        }
                    }
                } catch (error) {
                    console.warn('KtoOstatni: Błąd przetwarzania wizyty:', error, visit);
                }
            });
        } else {
            console.log('KtoOstatni: Brak wizyt w odpowiedzi API');
        }

        // Przetwórz rezerwacje (reservations)
        if (Array.isArray(apiDataContent.reservations) && apiDataContent.reservations.length > 0) {
            console.log(`KtoOstatni: Znaleziono ${apiDataContent.reservations.length} rezerwacji w odpowiedzi API`);

            apiDataContent.reservations.forEach((reservation, index) => {
                try {
                    console.log(`KtoOstatni: Przetwarzam rezerwację ${index + 1}:`, {
                        id: reservation.id,
                        product_id: reservation.product?.id,
                        patient: reservation.user?.name,
                        start: reservation.start_date,
                        end: reservation.end_date
                    });

                    // Użyj product.id z rezerwacji
                    const productId = reservation.product?.id;
                    if (!productId) {
                        console.warn('KtoOstatni: Rezerwacja nie ma product.id:', reservation);
                        return;
                    }

                    // Znajdź lekarza w mapie
                    const doctor = doctorMap.get(parseInt(productId));
                    if (!doctor) {
                        console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w mapie lekarzy`);
                        return;
                    }

                    // Przetwórz rezerwację (używamy tej samej funkcji co dla wizyt)
                    const processedAppointment = processVisitData(reservation);
                    if (processedAppointment) {
                        // Znajdź lekarza w tablicy doctors
                        const doctorIndex = doctors.findIndex(d => parseInt(d.doctorId) === parseInt(productId));
                        if (doctorIndex !== -1) {
                            doctors[doctorIndex].appointments.push(processedAppointment);
                            totalAppointments++;
                            console.log(`KtoOstatni: Dodano rezerwację do lekarza ${doctor.name}`);
                        } else {
                            console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w tablicy doctors`);
                        }
                    }
                } catch (error) {
                    console.warn('KtoOstatni: Błąd przetwarzania rezerwacji:', error, reservation);
                }
            });
        }
    } else {
        console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi API:', apiData);
    }

    // Upewnij się, że każdy lekarz ma pole appointments, nawet jeśli jest puste
    doctors.forEach(doctor => {
        if (!doctor.appointments) {
            doctor.appointments = [];
        }
    });

    // Przygotuj dane w nowym formacie
    const formattedDate = date.toISOString().split('T')[0]; // Format YYYY-MM-DD

    // Przygotuj dane w nowym formacie
    const daysData = [{
        date: formattedDate,
        doctors: doctors.map(doctor => ({
            doctorId: doctor.doctorId,
            doctorName: doctor.doctorName,
            appointments: doctor.appointments.map(appointment => ({
                appointmentId: appointment.appointmentId,
                patientFirstName: appointment.patientFirstName,
                patientLastName: appointment.patientLastName,
                appointmentStart: appointment.appointmentStart,
                appointmentEnd: appointment.appointmentEnd,
                appointmentDuration: appointment.appointmentDuration
            }))
        }))
    }];

    // Stwórz obiekt wynikowy w nowym formacie
    const result = {
        exportDate: new Date().toISOString(),
        syncCode: syncCode || "igab000000000001", // Użyj przekazanego kodu lub domyślnego
        syncData: {
            days: daysData
        }
    };

    console.log('KtoOstatni: Używam kodu synchronizacji w wyniku:', result.syncCode);

    console.log('KtoOstatni: Przetworzone dane w nowym formacie:', result);
    return result;
}

// Funkcja do przetwarzania danych wizyty/rezerwacji z API iGabinet
function processVisitData(visitData) {
    try {
        // Sprawdź wymagane pola
        if (!visitData.id || !visitData.start_date || !visitData.end_date) {
            console.warn('KtoOstatni: Wizyta nie ma wymaganych pól:', visitData);
            return null;
        }

        // Parsuj daty
        const startDate = new Date(visitData.start_date);
        const endDate = new Date(visitData.end_date);

        // Formatuj czasy
        const startTime = startDate.toLocaleTimeString('pl-PL', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const endTime = endDate.toLocaleTimeString('pl-PL', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Oblicz czas trwania w minutach
        const durationMinutes = Math.round((endDate - startDate) / 60000);

        // Wyciągnij dane pacjenta
        let patientFirstName = 'Pacjent';
        let patientLastName = '';

        if (visitData.user) {
            if (visitData.user.first_name && visitData.user.last_name) {
                patientFirstName = visitData.user.first_name;
                patientLastName = visitData.user.last_name;
            } else if (visitData.user.name) {
                // Podziel pełną nazwę na imię i nazwisko
                const nameParts = visitData.user.name.trim().split(' ');
                patientFirstName = nameParts[0] || 'Pacjent';
                patientLastName = nameParts.slice(1).join(' ') || '';
            }
        }

        const processedAppointment = {
            appointmentId: visitData.id.toString(),
            patientFirstName: patientFirstName,
            patientLastName: patientLastName,
            appointmentStart: startTime,
            appointmentEnd: endTime,
            appointmentDuration: durationMinutes
        };

        console.log('KtoOstatni: Przetworzona wizyta/rezerwacja:', processedAppointment);
        return processedAppointment;

    } catch (error) {
        console.error('KtoOstatni: Błąd przetwarzania danych wizyty:', error, visitData);
        return null;
    }
}

// Funkcje pomocnicze do formatowania danych
function extractFirstName(fullName) {
    // Usuń przedrostki jak "Wizyta: " lub "Rezerwacja: "
    const cleanName = fullName.replace(/^(Wizyta|Rezerwacja):\s*/i, '').trim();

    // Podziel na części i zwróć pierwszą część jako imię
    const parts = cleanName.split(' ');
    return parts.length > 0 ? parts[0] : 'Pacjent';
}

function extractLastName(fullName) {
    // Usuń przedrostki jak "Wizyta: " lub "Rezerwacja: "
    const cleanName = fullName.replace(/^(Wizyta|Rezerwacja):\s*/i, '').trim();

    // Podziel na części i zwróć pozostałe części jako nazwisko
    const parts = cleanName.split(' ');
    return parts.length > 1 ? parts.slice(1).join(' ') : '';
}

function calculateEndTime(startTime) {
    // Domyślna długość wizyty: 20 minut
    const [hours, minutes] = startTime.split(':').map(Number);
    let endMinutes = minutes + 20;
    let endHours = hours;

    if (endMinutes >= 60) {
        endMinutes -= 60;
        endHours += 1;
    }

    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
}

function calculateDuration(timeRange) {
    // Oblicz czas trwania wizyty w minutach
    const parts = timeRange.split(' - ');
    if (parts.length !== 2) return 20; // Domyślnie 20 minut

    const startParts = parts[0].split(':').map(Number);
    const endParts = parts[1].split(':').map(Number);

    const startMinutes = startParts[0] * 60 + startParts[1];
    const endMinutes = endParts[0] * 60 + endParts[1];

    return endMinutes - startMinutes;
}





// Funkcja do tworzenia danych testowych
function createTestScheduleData(doctorsDictionary, workingDoctorIds, date, syncCode = null) {
    console.log('KtoOstatni: Tworzę dane testowe dla daty:', date.toLocaleDateString('pl-PL'));
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length);
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);

    // Stwórz WSZYSTKICH lekarzy ze słownika (nie tylko pracujących)
    const doctors = doctorsDictionary.map((doctorFromDictionary) => {
        const id = parseInt(doctorFromDictionary.id);
        const doctorName = doctorFromDictionary.name || `Lekarz ID ${id}`;

        return {
            doctorId: `${id}`,
            doctorName: doctorName,
            appointments: []
        };
    });

    // Dodaj lekarzy pracujących, którzy nie są w słowniku (fallback)
    workingDoctorIds.forEach(doctorId => {
        const id = parseInt(doctorId);
        if (!isNaN(id) && !doctors.find(d => parseInt(d.doctorId) === id)) {
            console.warn(`KtoOstatni: Lekarz ID ${id} pracuje ale nie ma go w słowniku - dodaję z domyślną nazwą`);
            doctors.push({
                doctorId: `${id}`,
                doctorName: `Lekarz ID ${id}`,
                appointments: []
            });
        }
    });

    // Dodaj przykładowe wizyty tylko dla pierwszego lekarza ze słownika
    if (doctors.length > 0) {
        doctors[0].appointments.push({
            appointmentId: `test_${Date.now()}`,
            patientFirstName: 'Test',
            patientLastName: 'Pacjent',
            appointmentStart: '09:00',
            appointmentEnd: '09:30',
            appointmentDuration: 30
        });
    }

    // Przygotuj dane w nowym formacie
    const formattedDate = date.toISOString().split('T')[0]; // Format YYYY-MM-DD

    const result = {
        exportDate: new Date().toISOString(),
        syncCode: syncCode || "igab000000000001", // Użyj przekazanego kodu lub domyślnego
        syncData: {
            days: [{
                date: formattedDate,
                doctors: doctors
            }]
        }
    };

    console.log('KtoOstatni: Dane testowe w nowym formacie z kodem:', result.syncCode);
    console.log('KtoOstatni: Pełne dane testowe:', result);
    return result;
}
