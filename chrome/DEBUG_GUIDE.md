# 🔧 Przewodnik debugowania dodatku KtoOstatni

## 🚨 Problem: "Pobieranie danych z kolejnych dni"

### 📋 Kroki debugowania:

#### **Krok 1: Sprawdź konsolę deweloperską**
1. Otw<PERSON><PERSON> stronę iGabinet: `https://sonokard.igabinet.pl/admin/common_work_schedule.php`
2. Naciśnij **F12** aby otworzyć narzędzia deweloperskie
3. Przejdź do zakładki **Console**
4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stronę (F5)
5. Sprawdź logi:
   ```
   KtoOstatni: Content script załadowany na: https://sonokard.igabinet.pl/admin/common_work_schedule.php
   KtoOstatni: Chrome runtime dostępny: true
   KtoOstatni: isIGabinet: true, isTestPage: false
   KtoOstatni: Jesteśmy na odpowiedniej stronie
   KtoOstatni: Sygnał gotowości wysłany pomyślnie
   ```

#### **Krok 2: Ręczne wywołanie ekstrakcji danych**
1. W konsoli wpisz: 
   ```javascript
   extractScheduleFromPage().then(data => console.log('Wynik:', data)).catch(err => console.error('Błąd:', err));
   ```
2. Obserwuj logi w konsoli, które pokazują szczegółowy proces pobierania danych:
   ```
   KtoOstatni: Rozpoczynam pobieranie danych z API iGabinet...
   KtoOstatni: Aktualny URL: https://sonokard.igabinet.pl/admin/common_work_schedule.php
   KtoOstatni: Sprawdzam kolejne dni robocze w poszukiwaniu wizyt z API...
   KtoOstatni: Sprawdzam dzień 0: 26.08.2025
   KtoOstatni: Pobieram lekarzy dla dnia 26.08.2025
   KtoOstatni: Pobieranie lekarzy z API iGabinet dla daty: 2025-08-26T10:06:00.000Z
   KtoOstatni: Pobieranie lekarzy dla daty (ISO): 2025-08-26T10:06:00.000Z
   KtoOstatni: Wywołuję API getWorkingProducts z body: {"section":"getWorkingProducts","date":"2025-08-26T10:06:00.000Z","facility_id":[1]}
   KtoOstatni: Odpowiedź API getWorkingProducts: {...}
   KtoOstatni: Aktywni lekarze z API dla daty 26.08.2025: [...]
   KtoOstatni: Liczba lekarzy znalezionych: 5
   KtoOstatni: Znaleziono 5 lekarzy dla dnia 26.08.2025
   KtoOstatni: Pobieranie wizyt z API dla daty: 2025-08-26T10:06:00.000Z
   KtoOstatni: ID lekarzy do sprawdzenia: [43, 10, 20, 83, 1748423446]
   KtoOstatni: Pobieranie wizyt od 2025-08-26T00:00:00.000Z do 2025-08-26T23:59:59.999Z
   KtoOstatni: Wywołuję API getEntities z body: {"section":"getEntities","start_date":"2025-08-26T00:00:00.000Z","end_date":"2025-08-26T23:59:59.999Z","products":[43,10,20,83,1748423446]}
   KtoOstatni: Odpowiedź API getEntities: {...}
   KtoOstatni: Przetwarzam dane z API...
   KtoOstatni: Mapa lekarzy: [[43, {...}], [10, {...}], [20, {...}], [83, {...}], [1748423446, {...}]]
   KtoOstatni: Znaleziono 12 wizyt w odpowiedzi API
   KtoOstatni: Przetwarzam wizytę z API: {...}
   KtoOstatni: Przetworzona wizyta: {...}
   ...
   KtoOstatni: Przetworzone dane z API: {...}
   KtoOstatni: Znaleziono 12 wizyt w dniu 26.08.2025
   KtoOstatni: Znaleziono dane harmonogramu z API: {...}
   ```

### 🔍 Szczegóły implementacji:

#### **1. Pobieranie lekarzy dla konkretnej daty**
Rozszerzenie najpierw pobiera listę lekarzy dostępnych w danym dniu używając API:
```javascript
// Wywołanie API getWorkingProducts z datą
const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
    method: 'POST',
    headers: { ... },
    body: JSON.stringify({
        "section": "getWorkingProducts",
        "date": dateISO,
        "facility_id": [1]
    }),
    mode: 'cors',
    credentials: 'include'
});
```

#### **2. Pobieranie wizyt dla znalezionych lekarzy**
Następnie pobiera wizyty dla znalezionych lekarzy:
```javascript
// Wywołaj API getEntities
const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
    method: 'POST',
    headers: { ... },
    body: JSON.stringify({
        "section": "getEntities",
        "start_date": startDateISO,
        "end_date": endDateISO,
        "products": doctorIds
    }),
    mode: 'cors',
    credentials: 'include'
});
```

#### **3. Przetwarzanie danych**
Rozszerzenie przetwarza otrzymane dane, konwertując ID lekarzy na liczby całkowite:
```javascript
// Upewnij się, że ID lekarza jest liczbą całkowitą
const doctorId = parseInt(doctor.id);
doctorMap.set(doctorId, doctor);

// Sprawdź czy lekarz istnieje
const doctor = doctorMap.get(parseInt(appointment.product_id));
```

### 📝 Najważniejsze zmiany:

1. **Pobieranie lekarzy dla konkretnej daty** - zmiana z `getAvailableProducts` na `getWorkingProducts` z parametrem `date`
2. **Konwersja ID lekarzy na liczby całkowite** - użycie `parseInt()` do konwersji ID lekarzy
3. **Dodanie szczegółowych logów debugowania** - więcej informacji w konsoli
4. **Poprawiona obsługa błędów** - lepsze komunikaty błędów i więcej szczegółów

### 🚀 Testowanie:

1. **Test na stronie iGabinet:**
   ```javascript
   extractScheduleFromPage().then(data => console.log('Wynik:', data)).catch(err => console.error('Błąd:', err));
   ```

2. **Test z poziomu popup:**
   - Otwórz stronę iGabinet
   - Kliknij ikonę rozszerzenia
   - Wprowadź kod synchronizacji
   - Kliknij "🔄 Synchronizuj teraz"

3. **Test automatycznej synchronizacji:**
   - Włącz automatyczną synchronizację
   - Ustaw interwał (np. 5 minut)
   - Sprawdź czy dane są pobierane automatycznie

### ❌ Rozwiązywanie problemów:

1. **Brak lekarzy dla danej daty:**
   - Sprawdź czy data jest poprawna
   - Sprawdź czy API `getWorkingProducts` zwraca lekarzy
   - Sprawdź czy ID placówki jest poprawne (domyślnie `[1]`)

2. **Brak wizyt dla lekarzy:**
   - Sprawdź czy ID lekarzy są poprawne
   - Sprawdź czy zakres dat jest poprawny
   - Sprawdź czy API `getEntities` zwraca wizyty

3. **Błędy mapowania lekarzy:**
   - Sprawdź czy ID lekarzy są liczbami całkowitymi
   - Sprawdź czy mapa lekarzy jest poprawnie utworzona

---

**💡 Wskazówka:** Zawsze sprawdzaj konsolę deweloperską - to najlepsze narzędzie do debugowania!