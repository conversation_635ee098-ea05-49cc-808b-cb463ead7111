// Background script dla <PERSON>toOstatni - Automatyczna Synchronizacja
console.log('KtoOstatni: Background script za<PERSON><PERSON><PERSON><PERSON>');

// Konfiguracja domyślna
const DEFAULT_CONFIG = {
    syncInterval: 15, // minuty
    isEnabled: false,
    syncCode: '',
    lastSync: null,
    autoSync: false
};

// Inicjalizacja przy starcie
chrome.runtime.onStartup.addListener(async () => {
    console.log('KtoOstatni: Chrome startup - inicjalizacja');
    await initializeExtension();
});

chrome.runtime.onInstalled.addListener(async () => {
    console.log('KtoOstatni: Extension installed/updated - inicjalizacja');
    await initializeExtension();
});

// Inicjalizacja rozszerzenia
async function initializeExtension() {
    try {
        console.log('KtoOstatni: Rozpoczynam inicjalizację rozszerzenia...');

        const config = await getConfig();
        console.log('KtoOstatni: Aktualna konfiguracja:', {
            isEnabled: config.isEnabled,
            autoSync: config.autoSync,
            syncCode: config.syncCode ? `${config.syncCode.substring(0, 8)}...` : 'brak',
            syncInterval: config.syncInterval,
            lastSync: config.lastSync
        });

        // Wyczyść stare alarmy na wszelki wypadek
        await chrome.alarms.clear('autoSync');
        console.log('KtoOstatni: Wyczyszczono stare alarmy');

        if (config.autoSync && config.isEnabled && config.syncCode) {
            console.log('KtoOstatni: Konfiguracja automatycznej synchronizacji...');
            await setupAutoSync(config.syncInterval);

            // Sprawdź czy alarm został utworzony
            const alarms = await chrome.alarms.getAll();
            const autoSyncAlarm = alarms.find(alarm => alarm.name === 'autoSync');
            if (autoSyncAlarm) {
                console.log('KtoOstatni: Alarm automatycznej synchronizacji aktywny:', {
                    scheduledTime: new Date(autoSyncAlarm.scheduledTime).toLocaleString(),
                    periodInMinutes: autoSyncAlarm.periodInMinutes
                });
            } else {
                console.warn('KtoOstatni: Alarm automatycznej synchronizacji nie został utworzony!');
            }
        } else {
            console.log('KtoOstatni: Automatyczna synchronizacja wyłączona:', {
                autoSync: config.autoSync,
                isEnabled: config.isEnabled,
                hasSyncCode: !!config.syncCode
            });
        }

        console.log('KtoOstatni: Inicjalizacja rozszerzenia zakończona pomyślnie');
    } catch (error) {
        console.error('KtoOstatni: Błąd inicjalizacji:', error);
    }
}

// Pobierz konfigurację z storage
async function getConfig() {
    const result = await chrome.storage.local.get(DEFAULT_CONFIG);
    return { ...DEFAULT_CONFIG, ...result };
}

// Zapisz konfigurację
async function saveConfig(config) {
    await chrome.storage.local.set(config);
    console.log('KtoOstatni: Konfiguracja zapisana:', config);
}

// Konfiguracja automatycznej synchronizacji
async function setupAutoSync(intervalMinutes) {
    try {
        // Wyczyść istniejące alarmy
        await chrome.alarms.clear('autoSync');

        if (intervalMinutes > 0) {
            // Utwórz nowy alarm
            await chrome.alarms.create('autoSync', {
                delayInMinutes: intervalMinutes,
                periodInMinutes: intervalMinutes
            });
            console.log(`KtoOstatni: Auto-sync skonfigurowany na ${intervalMinutes} minut`);
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd konfiguracji auto-sync:', error);
    }
}

// Obsługa alarmów
chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'autoSync') {
        console.log('KtoOstatni: Uruchamiam automatyczną synchronizację');
        await performAutoSync();
    }
});

// Wykonaj automatyczną synchronizację
async function performAutoSync() {
    try {
        const config = await getConfig();

        if (!config.isEnabled || !config.syncCode) {
            console.log('KtoOstatni: Auto-sync wyłączony lub brak kodu synchronizacji');
            return;
        }

        console.log('KtoOstatni: Rozpoczynam automatyczną synchronizację...');

        // Znajdź kartę z iGabinet lub localhost (dla testów)
        const tabs = await chrome.tabs.query({
            url: ['*://*.igabinet.pl/*', 'http://localhost/*', 'https://localhost/*']
        });

        if (tabs.length === 0) {
            console.log('KtoOstatni: Brak otwartych kart iGabinet lub localhost - pomijam synchronizację');
            console.log('KtoOstatni: Aby synchronizacja działała w tle, otwórz kartę z iGabinet lub stroną testową');
            return;
        }

        // Znajdź najlepszą kartę (preferuj iGabinet nad localhost)
        let targetTab = tabs.find(tab => tab.url.includes('igabinet.pl'));
        if (!targetTab) {
            targetTab = tabs.find(tab => tab.url.includes('localhost') &&
                (tab.url.includes('test_page.html') || tab.url.includes('igabinet')));
        }
        if (!targetTab) {
            targetTab = tabs[0]; // Fallback na pierwszą dostępną kartę
        }

        console.log('KtoOstatni: Znaleziono kartę do synchronizacji:', targetTab.url);

        // Sprawdź czy content script jest załadowany
        let contentScriptReady = false;
        try {
            const pingResponse = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('Timeout')), 2000);
                chrome.tabs.sendMessage(targetTab.id, { action: 'ping' }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            contentScriptReady = pingResponse && pingResponse.success;
        } catch (error) {
            console.log('KtoOstatni: Content script nie odpowiada, próbuję wstrzyknąć:', error.message);
        }

        // Jeśli content script nie jest gotowy, spróbuj go wstrzyknąć
        if (!contentScriptReady) {
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: targetTab.id },
                    files: ['content.js']
                });
                console.log('KtoOstatni: Content script wstrzyknięty pomyślnie');

                // Poczekaj chwilę na inicjalizację
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error('KtoOstatni: Nie można wstrzyknąć content script:', error);
                return;
            }
        }

        // Wyślij żądanie do content script z kodem synchronizacji
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Timeout podczas wyciągania danych')), 30000);
            chrome.tabs.sendMessage(targetTab.id, {
                action: 'extractScheduleData',
                syncCode: config.syncCode
            }, (response) => {
                clearTimeout(timeout);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });

        if (response && response.success && response.data) {
            // Wyślij dane do systemu KtoOstatni
            const syncResult = await sendDataToSystem(config.syncCode, response.data);

            if (syncResult.success) {
                // Zapisz czas ostatniej synchronizacji
                await saveConfig({
                    ...config,
                    lastSync: new Date().toISOString()
                });

                console.log('KtoOstatni: Automatyczna synchronizacja zakończona pomyślnie');

                // Wyślij notyfikację do popup (jeśli jest otwarty)
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: true,
                    data: syncResult
                }).catch(() => { }); // Ignoruj błąd jeśli popup nie jest otwarty

            } else {
                console.error('KtoOstatni: Błąd synchronizacji:', syncResult.error);

                // Wyślij notyfikację o błędzie
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: false,
                    error: syncResult.error
                }).catch(() => { });
            }
        } else {
            const errorMsg = response?.error || 'Brak odpowiedzi od content script';
            console.error('KtoOstatni: Błąd wyciągania danych:', errorMsg);

            // Wyślij notyfikację o błędzie
            chrome.runtime.sendMessage({
                action: 'syncCompleted',
                success: false,
                error: errorMsg
            }).catch(() => { });
        }

    } catch (error) {
        console.error('KtoOstatni: Błąd automatycznej synchronizacji:', error);

        // Wyślij notyfikację o błędzie
        chrome.runtime.sendMessage({
            action: 'syncCompleted',
            success: false,
            error: error.message
        }).catch(() => { });
    }
}

// Wyślij dane do systemu KtoOstatni
async function sendDataToSystem(syncCode, scheduleData) {
    try {
        console.log('KtoOstatni: Wysyłanie danych do systemu');
        console.log('KtoOstatni: Kod synchronizacji z konfiguracji:', syncCode);
        console.log('KtoOstatni: Kod synchronizacji w danych:', scheduleData.syncCode);
        console.log('KtoOstatni: Struktura danych do wysłania:', {
            syncCode: scheduleData.syncCode || syncCode,
            hasSyncData: !!scheduleData.syncData,
            daysCount: scheduleData.syncData?.days?.length || 0
        });

        // Upewnij się, że dane mają ustawiony kod synchronizacji
        if (!scheduleData.syncCode) {
            scheduleData.syncCode = syncCode;
            console.log('KtoOstatni: Ustawiono kod synchronizacji z konfiguracji:', syncCode);
        }

        console.log('KtoOstatni: Finalne dane do wysłania (JSON):', JSON.stringify(scheduleData, null, 2));

        // Dodaj szczegółowe logowanie
        if (scheduleData.syncData && scheduleData.syncData.days) {
            let totalDoctors = 0;
            let totalAppointments = 0;

            scheduleData.syncData.days.forEach(day => {
                if (day.doctors && Array.isArray(day.doctors)) {
                    totalDoctors += day.doctors.length;

                    day.doctors.forEach((doctor, index) => {
                        const appointmentsCount = doctor.appointments ? doctor.appointments.length : 0;
                        totalAppointments += appointmentsCount;

                        console.log(`KtoOstatni: Dzień ${day.date}, Lekarz ${index + 1}:`, {
                            id: doctor.doctorId,
                            name: doctor.doctorName,
                            appointments: appointmentsCount
                        });

                        // Loguj pierwsze 3 wizyty dla tego lekarza (jeśli istnieją)
                        if (doctor.appointments && doctor.appointments.length > 0) {
                            console.log(`KtoOstatni: Przykładowe wizyty dla lekarza ${doctor.doctorName}:`);
                            doctor.appointments.slice(0, 3).forEach((appointment, i) => {
                                console.log(`KtoOstatni: Wizyta ${i + 1}:`, {
                                    id: appointment.appointmentId,
                                    patient: `${appointment.patientFirstName} ${appointment.patientLastName}`,
                                    time: `${appointment.appointmentStart} - ${appointment.appointmentEnd}`,
                                    duration: appointment.appointmentDuration
                                });
                            });
                        }
                    });
                }
            });

            console.log('KtoOstatni: Łączna liczba lekarzy:', totalDoctors);
            console.log('KtoOstatni: Łączna liczba wizyt:', totalAppointments);
        }

        // Przygotuj dane w formacie oczekiwanym przez API
        const apiData = {
            sync_code: scheduleData.syncCode || syncCode,
            data: scheduleData.syncData || scheduleData
        };

        console.log('KtoOstatni: scheduleData.syncCode:', scheduleData.syncCode);
        console.log('KtoOstatni: syncCode z parametru:', syncCode);
        console.log('KtoOstatni: scheduleData.syncData:', scheduleData.syncData);
        console.log('KtoOstatni: scheduleData (cały obiekt):', scheduleData);
        console.log('KtoOstatni: Finalne apiData.sync_code:', apiData.sync_code);
        console.log('KtoOstatni: Finalne apiData.data:', apiData.data);
        console.log('KtoOstatni: Dane w formacie API (JSON):', JSON.stringify(apiData, null, 2));

        const response = await fetch('http://localhost:8080/api/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Extension-Version': '2.1.0'
            },
            body: JSON.stringify(apiData)
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas wysyłania danych:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const result = await response.json();
        console.log('KtoOstatni: Odpowiedź z API:', result);
        return { success: true, data: result };

    } catch (error) {
        console.error('KtoOstatni: Błąd wysyłania danych:', error);
        return { success: false, error: error.message };
    }
}

// Nasłuchuj na wiadomości z popup i content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('KtoOstatni: Background otrzymał wiadomość:', request);

    if (request.action === 'contentScriptReady') {
        console.log('KtoOstatni: Content script gotowy na:', request.url);
        sendResponse({ success: true, message: 'Background script otrzymał sygnał' });
        return true;
    }

    if (request.action === 'getConfig') {
        getConfig().then(config => {
            sendResponse({ success: true, config: config });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'saveConfig') {
        saveConfig(request.config).then(async () => {
            console.log('KtoOstatni: Zapisano konfigurację, aktualizuję auto-sync...');

            // Zaktualizuj auto-sync
            if (request.config.autoSync && request.config.isEnabled && request.config.syncCode) {
                console.log(`KtoOstatni: Włączam auto-sync z interwałem ${request.config.syncInterval} minut`);
                await setupAutoSync(request.config.syncInterval);
            } else {
                console.log('KtoOstatni: Wyłączam auto-sync');
                await chrome.alarms.clear('autoSync');
            }

            // Sprawdź status alarmów
            const alarms = await chrome.alarms.getAll();
            console.log('KtoOstatni: Aktywne alarmy:', alarms.map(a => ({
                name: a.name,
                scheduledTime: new Date(a.scheduledTime).toLocaleString(),
                periodInMinutes: a.periodInMinutes
            })));

            sendResponse({ success: true });
        }).catch(error => {
            console.error('KtoOstatni: Błąd zapisywania konfiguracji:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'manualSync') {
        console.log('KtoOstatni: Rozpoczynam ręczną synchronizację...');
        performAutoSync().then(() => {
            sendResponse({ success: true });
        }).catch(error => {
            console.error('KtoOstatni: Błąd ręcznej synchronizacji:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'getAlarmStatus') {
        chrome.alarms.getAll().then(alarms => {
            const autoSyncAlarm = alarms.find(alarm => alarm.name === 'autoSync');
            sendResponse({
                success: true,
                hasAlarm: !!autoSyncAlarm,
                alarmInfo: autoSyncAlarm ? {
                    scheduledTime: new Date(autoSyncAlarm.scheduledTime).toLocaleString(),
                    periodInMinutes: autoSyncAlarm.periodInMinutes
                } : null,
                allAlarms: alarms.map(a => ({
                    name: a.name,
                    scheduledTime: new Date(a.scheduledTime).toLocaleString(),
                    periodInMinutes: a.periodInMinutes
                }))
            });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'downloadData') {
        // Pobierz dane jako plik JSON
        const jsonData = JSON.stringify(request.data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const filename = `ktoostatni_sync_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;

        chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('KtoOstatni: Błąd podczas pobierania:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('KtoOstatni: Plik został pobrany z ID:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }

            // Zwolnij URL
            URL.revokeObjectURL(url);
        });

        return true;
    }
});
