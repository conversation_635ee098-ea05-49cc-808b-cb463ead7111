<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KtoOstatni - Ustawienia</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 32px;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h2 {
            margin: 0 0 20px 0;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input::placeholder, .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            background: rgba(255, 255, 255, 0.15);
        }

        .form-group select option {
            background: #333;
            color: white;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        button.primary {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        button.primary:hover {
            background: #3d8bfe;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
        }

        button.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
        }

        button.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        button.danger {
            background: #ff4757;
            color: white;
            border-color: #ff4757;
        }

        button.danger:hover {
            background: #ff3742;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
        }

        .status {
            text-align: center;
            margin: 20px 0;
            padding: 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 14px;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status.success {
            background: rgba(46, 213, 115, 0.2);
            border: 1px solid rgba(46, 213, 115, 0.3);
        }

        .status.error {
            background: rgba(255, 71, 87, 0.2);
            border: 1px solid rgba(255, 71, 87, 0.3);
        }

        .info-box {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid #4facfe;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .info-box h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .info-box p {
            margin: 0;
            font-size: 13px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .label {
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 KtoOstatni - Ustawienia</h1>
            <p>Konfiguracja automatycznej synchronizacji wizyt z iGabinet</p>
        </div>

        <!-- Główna konfiguracja -->
        <div class="card">
            <h2>⚙️ Główna konfiguracja</h2>
            
            <div class="form-group">
                <label for="syncCode">Kod synchronizacji:</label>
                <input type="text" id="syncCode" placeholder="Wprowadź kod synchronizacji otrzymany od administratora">
            </div>

            <div class="form-group">
                <label for="syncInterval">Interwał automatycznej synchronizacji:</label>
                <select id="syncInterval">
                    <option value="1">Co minutę (tylko do testów)</option>
                    <option value="5">Co 5 minut</option>
                    <option value="10">Co 10 minut</option>
                    <option value="15" selected>Co 15 minut</option>
                    <option value="30">Co 30 minut</option>
                    <option value="60">Co godzinę</option>
                    <option value="120">Co 2 godziny</option>
                    <option value="240">Co 4 godziny</option>
                </select>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="autoSync">
                <label for="autoSync">Włącz automatyczną synchronizację w tle</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="isEnabled">
                <label for="isEnabled">Dodatek aktywny</label>
            </div>
        </div>

        <!-- Statystyki -->
        <div class="card">
            <h2>📊 Statystyki</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="value" id="lastSyncTime">-</div>
                    <div class="label">Ostatnia synchronizacja</div>
                </div>
                <div class="stat-card">
                    <div class="value" id="syncCount">0</div>
                    <div class="label">Liczba synchronizacji</div>
                </div>
                <div class="stat-card">
                    <div class="value" id="nextSyncTime">-</div>
                    <div class="label">Następna synchronizacja</div>
                </div>
            </div>
        </div>

        <!-- Informacje -->
        <div class="card">
            <h2>ℹ️ Informacje</h2>
            
            <div class="info-box">
                <h4>Jak to działa?</h4>
                <p>Dodatek automatycznie synchronizuje wizyty z harmonogramu iGabinet do systemu KtoOstatni w określonych interwałach. Musisz mieć otwartą kartę z iGabinet w przeglądarce.</p>
            </div>

            <div class="info-box">
                <h4>Wymagania</h4>
                <p>• Kod synchronizacji otrzymany od administratora systemu KtoOstatni<br>
                   • Otwarta karta z harmonogramem iGabinet<br>
                   • Aktywne połączenie internetowe</p>
            </div>
        </div>

        <!-- Przyciski -->
        <div class="button-group">
            <button id="saveButton" class="primary">💾 Zapisz ustawienia</button>
            <button id="testButton" class="secondary">🧪 Test synchronizacji</button>
            <button id="resetButton" class="danger">🔄 Resetuj ustawienia</button>
        </div>

        <div class="status" id="status">Gotowy do konfiguracji</div>
    </div>

    <script src="options.js"></script>
</body>
</html>
