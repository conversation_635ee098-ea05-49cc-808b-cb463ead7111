# 🔧 Naprawa Synchronizacji w Tle - Dodatek KtoOstatni

## 🚨 Problem
Dodatek Chrome nie synchronizuje danych w tle automatycznie.

## ✅ Rozwiązanie

### Krok 1: Zainstaluj/Zaktualizuj Dodatek

1. **Otwórz Chrome Extensions:**
   - <PERSON><PERSON>z w pasku adresu: `chrome://extensions/`
   - <PERSON>łącz "Tryb dewelopera" (prawy górny róg)

2. **Załaduj dodatek:**
   - Klik<PERSON>j "Załaduj rozpakowane"
   - <PERSON><PERSON><PERSON><PERSON> katalog `chrome1/` z tego projektu
   - Jeśli dodatek już istnieje, kliknij ikonę odświeżania (🔄)

3. **Sprawdź uprawnienia:**
   - Klik<PERSON>j "Szczegóły" przy dodatku
   - Upewnij się, że ma dostęp do `igabinet.pl` i `localhost`

### Krok 2: Przetestuj Dodatek

1. **Otw<PERSON><PERSON> stronę debug:**
   ```
   http://localhost:8080/chrome1/debug_extension.html
   ```

2. **Sprawdź status dodatku:**
   - Kliknij "Sprawdź Status"
   - Powinieneś zobaczyć "✅ Dodatek aktywny"

3. **Skonfiguruj synchronizację:**
   - Kod synchronizacji: `igab000000000001`
   - Interwał: `5 minut` (dla testów)
   - Kliknij "Zapisz Konfigurację"

4. **Sprawdź alarmy:**
   - Kliknij "Sprawdź Alarmy"
   - Powinieneś zobaczyć "✅ Alarm auto-sync aktywny"

### Krok 3: Test Ręcznej Synchronizacji

1. **Otwórz kartę testową:**
   ```
   http://localhost:8080/chrome1/test_page.html
   ```

2. **Na stronie debug kliknij:**
   - "Test Ręcznej Synchronizacji"
   - Powinieneś zobaczyć "✅ Synchronizacja zakończona pomyślnie"

### Krok 4: Sprawdź Background Script

1. **Otwórz background page:**
   - Idź do `chrome://extensions/`
   - Znajdź "KtoOstatni" i kliknij "Szczegóły"
   - Kliknij "Sprawdź widoki: service worker"
   - Otwórz konsolę (F12)

2. **Sprawdź logi:**
   ```
   KtoOstatni: Extension installed/updated - inicjalizacja
   KtoOstatni: Rozpoczynam inicjalizację rozszerzenia...
   KtoOstatni: Aktualna konfiguracja: {...}
   KtoOstatni: Alarm automatycznej synchronizacji aktywny: {...}
   ```

### Krok 5: Test Automatycznej Synchronizacji

1. **Pozostaw otwarte karty:**
   - `http://localhost:8080/chrome1/test_page.html`
   - `http://localhost:8080/chrome1/debug_extension.html`

2. **Poczekaj 5 minut** (lub ustawiony interwał)

3. **Sprawdź logi w background script:**
   ```
   KtoOstatni: Uruchamiam automatyczną synchronizację
   KtoOstatni: Rozpoczynam automatyczną synchronizację...
   KtoOstatni: Znaleziono kartę do synchronizacji: http://localhost:8080/chrome1/test_page.html
   KtoOstatni: Automatyczna synchronizacja zakończona pomyślnie
   ```

## 🔍 Rozwiązywanie Problemów

### Problem: "Dodatek nie jest zainstalowany"
**Rozwiązanie:**
- Sprawdź czy katalog `chrome1/` zawiera wszystkie pliki
- Upewnij się, że tryb dewelopera jest włączony
- Przeładuj dodatek w `chrome://extensions/`

### Problem: "Brak aktywnego alarmu auto-sync"
**Rozwiązanie:**
1. Otwórz popup dodatku (kliknij ikonę w pasku narzędzi)
2. Wprowadź kod synchronizacji: `igab000000000001`
3. Włącz przełącznik ON/OFF
4. Sprawdź czy status zmienił się na "Aktywny"

### Problem: "Brak otwartych kart iGabinet lub localhost"
**Rozwiązanie:**
- Otwórz kartę: `http://localhost:8080/chrome1/test_page.html`
- Lub prawdziwą stronę iGabinet: `https://sonokard.igabinet.pl/`
- Dodatek wymaga otwartej karty do działania w tle

### Problem: "Content script nie odpowiada"
**Rozwiązanie:**
1. Odśwież kartę z iGabinet/test_page.html
2. Sprawdź konsolę na tej karcie (F12):
   ```
   KtoOstatni: Content script załadowany na: ...
   KtoOstatni: Jesteśmy na odpowiedniej stronie
   ```
3. Jeśli brak logów, przeładuj dodatek

### Problem: "Błąd HTTP podczas wysyłania danych"
**Rozwiązanie:**
- Sprawdź czy serwer działa: `http://localhost:8080/admin/api/import`
- Sprawdź czy kod synchronizacji istnieje w bazie danych
- Sprawdź logi serwera PHP

## 📊 Sprawdzenie Wyników

### Baza Danych
```sql
-- Sprawdź czy dane zostały zaimportowane
SELECT * FROM queue_appointments WHERE external_id LIKE 'test_%' ORDER BY created_at DESC LIMIT 5;

-- Sprawdź logi synchronizacji
SELECT * FROM sync_logs ORDER BY started_at DESC LIMIT 5;
```

### Panel Administracyjny
- **Import settings:** `http://localhost:8080/admin/client/import`
- **Mapowania lekarzy:** `http://localhost:8080/admin/client/import/doctor-mappings/3`
- **Test API:** `http://localhost:8080/admin/test_chrome_extension.html`

## 🎯 Oczekiwane Rezultaty

Po poprawnej konfiguracji:

1. **✅ Alarm aktywny** - widoczny w debug page
2. **✅ Automatyczna synchronizacja** - co 5 minut (lub ustawiony interwał)
3. **✅ Logi w background script** - potwierdzające działanie
4. **✅ Dane w bazie** - nowe wizyty w tabeli `queue_appointments`
5. **✅ Status w popup** - "Aktywny" z czasem ostatniej synchronizacji

## 🚀 Najważniejsze Zmiany

### Ulepszone funkcje background.js:
- ✅ Lepsze wyszukiwanie kart (iGabinet + localhost)
- ✅ Automatyczne wstrzykiwanie content script
- ✅ Zwiększone timeouty i obsługa błędów
- ✅ Szczegółowe logowanie
- ✅ Notyfikacje do popup

### Nowe funkcje debug:
- ✅ Strona debug: `chrome1/debug_extension.html`
- ✅ Sprawdzanie statusu alarmów
- ✅ Test ręcznej synchronizacji
- ✅ Monitoring w czasie rzeczywistym

### Poprawiona komunikacja:
- ✅ Ping/pong między background a content script
- ✅ Notyfikacje o zakończeniu synchronizacji
- ✅ Status alarmów w popup

## 📞 Wsparcie

Jeśli synchronizacja nadal nie działa:

1. **Sprawdź logi background script** - najważniejsze źródło informacji
2. **Użyj strony debug** - `chrome1/debug_extension.html`
3. **Sprawdź konsolę content script** - na karcie z iGabinet
4. **Sprawdź logi serwera** - czy API odpowiada poprawnie

---

**🎉 Powodzenia z naprawą synchronizacji!**
