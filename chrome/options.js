// Options script dla <PERSON> - Automatyczna Synchronizacja
document.addEventListener('DOMContentLoaded', function () {
    console.log('KtoOstatni: Options page załadowana');

    // Elementy UI
    const syncCodeInput = document.getElementById('syncCode');
    const syncIntervalSelect = document.getElementById('syncInterval');
    const autoSyncCheckbox = document.getElementById('autoSync');
    const isEnabledCheckbox = document.getElementById('isEnabled');
    const saveButton = document.getElementById('saveButton');
    const testButton = document.getElementById('testButton');
    const resetButton = document.getElementById('resetButton');
    const statusElement = document.getElementById('status');
    const lastSyncTimeElement = document.getElementById('lastSyncTime');
    const syncCountElement = document.getElementById('syncCount');
    const nextSyncTimeElement = document.getElementById('nextSyncTime');

    let currentConfig = {};

    // Inicjalizacja
    init();

    async function init() {
        try {
            await loadConfig();
            updateUI();
            setupEventListeners();
            updateStats();
        } catch (error) {
            console.error('KtoOstatni: Błąd inicjalizacji options:', error);
            updateStatus('Błąd inicjalizacji: ' + error.message, 'error');
        }
    }

    // Załaduj konfigurację z background script
    async function loadConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    currentConfig = response.config;
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Zapisz konfigurację
    async function saveConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ 
                action: 'saveConfig', 
                config: currentConfig 
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Konfiguracja event listenerów
    function setupEventListeners() {
        // Zapisz ustawienia
        saveButton.addEventListener('click', async () => {
            await saveSettings();
        });

        // Test synchronizacji
        testButton.addEventListener('click', async () => {
            await testSync();
        });

        // Reset ustawień
        resetButton.addEventListener('click', async () => {
            if (confirm('Czy na pewno chcesz zresetować wszystkie ustawienia?')) {
                await resetSettings();
            }
        });

        // Auto-save przy zmianie pól
        syncCodeInput.addEventListener('change', () => {
            currentConfig.syncCode = syncCodeInput.value.trim();
        });

        syncIntervalSelect.addEventListener('change', () => {
            currentConfig.syncInterval = parseInt(syncIntervalSelect.value);
        });

        autoSyncCheckbox.addEventListener('change', () => {
            currentConfig.autoSync = autoSyncCheckbox.checked;
        });

        isEnabledCheckbox.addEventListener('change', () => {
            currentConfig.isEnabled = isEnabledCheckbox.checked;
        });
    }

    // Aktualizuj interfejs użytkownika
    function updateUI() {
        syncCodeInput.value = currentConfig.syncCode || '';
        syncIntervalSelect.value = currentConfig.syncInterval || 15;
        autoSyncCheckbox.checked = currentConfig.autoSync || false;
        isEnabledCheckbox.checked = currentConfig.isEnabled || false;
    }

    // Aktualizuj statystyki
    function updateStats() {
        // Ostatnia synchronizacja
        if (currentConfig.lastSync) {
            const lastSyncDate = new Date(currentConfig.lastSync);
            lastSyncTimeElement.textContent = lastSyncDate.toLocaleString('pl-PL');
        } else {
            lastSyncTimeElement.textContent = 'Nigdy';
        }

        // Liczba synchronizacji (symulowana)
        syncCountElement.textContent = currentConfig.syncCount || 0;

        // Następna synchronizacja
        if (currentConfig.isEnabled && currentConfig.autoSync && currentConfig.lastSync) {
            const nextSync = new Date(currentConfig.lastSync);
            nextSync.setMinutes(nextSync.getMinutes() + (currentConfig.syncInterval || 15));
            nextSyncTimeElement.textContent = nextSync.toLocaleString('pl-PL');
        } else {
            nextSyncTimeElement.textContent = 'Wyłączona';
        }
    }

    // Zapisz ustawienia
    async function saveSettings() {
        try {
            setButtonsEnabled(false);
            updateStatus('Zapisywanie ustawień...', '');

            // Walidacja
            if (!currentConfig.syncCode || currentConfig.syncCode.length < 5) {
                updateStatus('Błąd: Kod synchronizacji musi mieć co najmniej 5 znaków', 'error');
                return;
            }

            if (currentConfig.syncInterval < 1) {
                updateStatus('Błąd: Interwał synchronizacji musi być większy niż 0', 'error');
                return;
            }

            await saveConfig();
            updateStats();
            updateStatus('Ustawienia zostały zapisane pomyślnie', 'success');

        } catch (error) {
            console.error('KtoOstatni: Błąd zapisywania ustawień:', error);
            updateStatus('Błąd zapisywania: ' + error.message, 'error');
        } finally {
            setButtonsEnabled(true);
        }
    }

    // Test synchronizacji
    async function testSync() {
        try {
            setButtonsEnabled(false);
            updateStatus('Wykonywanie testu synchronizacji...', '');

            // Sprawdź czy konfiguracja jest poprawna
            if (!currentConfig.syncCode) {
                updateStatus('Błąd: Brak kodu synchronizacji', 'error');
                return;
            }

            // Wykonaj test synchronizacji
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ action: 'manualSync' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response.success) {
                updateStatus('Test synchronizacji zakończony pomyślnie', 'success');
                await loadConfig();
                updateStats();
            } else {
                updateStatus('Test synchronizacji nieudany: ' + response.error, 'error');
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd testu synchronizacji:', error);
            updateStatus('Błąd testu: ' + error.message, 'error');
        } finally {
            setButtonsEnabled(true);
        }
    }

    // Reset ustawień
    async function resetSettings() {
        try {
            setButtonsEnabled(false);
            updateStatus('Resetowanie ustawień...', '');

            // Domyślna konfiguracja
            currentConfig = {
                syncInterval: 15,
                isEnabled: false,
                syncCode: '',
                lastSync: null,
                autoSync: false,
                syncCount: 0
            };

            await saveConfig();
            updateUI();
            updateStats();
            updateStatus('Ustawienia zostały zresetowane', 'success');

        } catch (error) {
            console.error('KtoOstatni: Błąd resetowania ustawień:', error);
            updateStatus('Błąd resetowania: ' + error.message, 'error');
        } finally {
            setButtonsEnabled(true);
        }
    }

    // Funkcje pomocnicze
    function updateStatus(message, type = '') {
        statusElement.textContent = message;
        statusElement.className = 'status';
        
        if (type) {
            statusElement.classList.add(type);
        }

        console.log('KtoOstatni Options Status:', message);

        // Wyczyść status po 5 sekundach
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                statusElement.textContent = 'Gotowy do konfiguracji';
                statusElement.className = 'status';
            }, 5000);
        }
    }

    function setButtonsEnabled(enabled) {
        saveButton.disabled = !enabled;
        testButton.disabled = !enabled;
        resetButton.disabled = !enabled;
    }

    // Automatyczne odświeżanie statystyk co minutę
    setInterval(() => {
        updateStats();
    }, 60000);
});
