<?php
/**
 * API Application Entry Point
 * Obsługuje wszystkie żądania API dla systemu KtoOstatni
 */

// Załaduj konfigurację
require_once __DIR__ . '/../config.php';

// Ustaw nagłówki dla API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Autoloader dla klas API
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/controllers/' . $className . '.php',
        __DIR__ . '/models/' . $className . '.php',
        __DIR__ . '/core/' . $className . '.php',
        __DIR__ . '/../admin/models/' . $className . '.php', // Współdzielone modele
        __DIR__ . '/../admin/core/' . $className . '.php'    // Współdzielone core classes
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Inicjalizacja bazy danych
Database::init();

// Pobierz ścieżkę API
$request_uri = $_SERVER['REQUEST_URI'];

// Usuń /api/ z początku URI jeśli istnieje
if (strpos($request_uri, '/api/') === 0) {
    $request_uri = substr($request_uri, 4);
} elseif ($request_uri === '/api') {
    $request_uri = '/';
}

// Upewnij się, że $request_uri zawsze zaczyna się od ukośnika
if ($request_uri !== '' && $request_uri[0] !== '/') {
    $request_uri = '/' . $request_uri;
}

// Aktualizuj REQUEST_URI dla routera
$_SERVER['REQUEST_URI'] = $request_uri;

// Definicja tras API
$router = new ApiRouter();

// API dla systemu kolejkowego
$router->get('/queue/{clientId}', [QueueApiController::class, 'getQueueStatus']);
$router->get('/queue/{clientId}/changes', [QueueApiController::class, 'getQueueChanges']);

// API dla lekarzy (PWA)
$router->post('/doctor/login', [DoctorApiController::class, 'login']);
$router->get('/doctor/appointments/{roomId}', [DoctorApiController::class, 'appointments']);
$router->post('/doctor/call-next/{roomId}', [DoctorApiController::class, 'callNext']);
$router->post('/doctor/previous/{roomId}', [DoctorApiController::class, 'previous']);
$router->post('/doctor/skip-current/{roomId}', [DoctorApiController::class, 'skipCurrent']);
$router->get('/doctor/stats/{roomId}', [DoctorApiController::class, 'stats']);
$router->post('/doctor/check-room-availability', [DoctorApiController::class, 'checkRoomAvailability']);

// API dla wyświetlania reklam
$router->get('/ads/{clientId}', [AdsApiController::class, 'getAds']);
$router->post('/ads/view', [AdsApiController::class, 'recordView']);

// API dla importu danych z Chrome
$router->post('/import', [ImportApiController::class, 'import']);
$router->get('/import/status/{syncCode}', [ImportApiController::class, 'status']);
$router->post('/import/auto-map/{syncCode}', [ImportApiController::class, 'autoMap']);

// API dla wyświetlaczy
$router->get('/display/{code}', [DisplayApiController::class, 'getDisplayData']);
$router->post('/display/{code}/heartbeat', [DisplayApiController::class, 'heartbeat']);

// Uruchom router
$router->run();
