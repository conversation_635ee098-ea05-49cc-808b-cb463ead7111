<?php

class ApiController {
    protected $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Wyś<PERSON>j odpowiedź JSON
     */
    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Wyślij błąd JSON
     */
    protected function error($message, $statusCode = 400, $details = null) {
        $response = [
            'error' => true,
            'message' => $message,
            'timestamp' => date('c')
        ];
        
        if ($details && Config::isDebug()) {
            $response['details'] = $details;
        }
        
        $this->json($response, $statusCode);
    }
    
    /**
     * Wyślij sukces JSON
     */
    protected function success($data = null, $message = 'Success') {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('c')
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->json($response);
    }
    
    /**
     * Pobierz dane z żądania POST/PUT
     */
    protected function getRequestData() {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('Invalid JSON data');
        }
        
        return $data ?: [];
    }
    
    /**
     * Walidacja wymaganych pól
     */
    protected function validateRequired($data, $requiredFields) {
        $missing = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->error('Missing required fields: ' . implode(', ', $missing), 422);
        }
    }
    
    /**
     * Sprawdź autoryzację
     */
    protected function requireAuth($role = null) {
        session_start();
        
        if (!isset($_SESSION['user_id'])) {
            $this->error('Authentication required', 401);
        }
        
        if ($role) {
            $user = $this->getCurrentUser();
            if (!$user || $user['role'] !== $role) {
                $this->error('Insufficient permissions', 403);
            }
        }
        
        return true;
    }
    
    /**
     * Pobierz aktualnego użytkownika
     */
    protected function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    
    /**
     * Sprawdź token CSRF (dla POST/PUT/DELETE)
     */
    protected function validateCsrfToken() {
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT', 'DELETE'])) {
            $token = $_POST[CSRF_TOKEN_NAME] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
            
            if (!$token || !hash_equals($_SESSION['csrf_token'] ?? '', $token)) {
                $this->error('Invalid CSRF token', 403);
            }
        }
    }
    
    /**
     * Logowanie API
     */
    protected function log($message, $level = 'INFO') {
        if (Config::isModuleEnabled('log')) {
            $logMessage = sprintf(
                "[%s] [%s] [API] %s\n",
                date('Y-m-d H:i:s'),
                $level,
                $message
            );
            
            error_log($logMessage, 3, LOG_FILE);
        }
    }
    
    /**
     * Sprawdź limit żądań (rate limiting)
     */
    protected function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
        // Implementacja rate limiting - można rozszerzyć w przyszłości
        return true;
    }
    
    /**
     * Sanityzacja danych wejściowych
     */
    protected function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
}
