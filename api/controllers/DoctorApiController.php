<?php

class DoctorApiController extends ApiController {

    /**
     * Logowanie lekarza
     */
    public function login() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['access_code']);

            $accessCode = $data['access_code'];

            // Znajdź lekarza po kodzie dostępu
            $stmt = $this->db->prepare("
                SELECT d.*, u.company_name, qr.name as room_name, qr.id as room_id
                FROM queue_doctors d
                JOIN users u ON d.client_id = u.id
                LEFT JOIN queue_rooms qr ON d.default_room_id = qr.id
                WHERE d.access_code = ? AND d.active = 1
            ");
            $stmt->execute([$accessCode]);
            $doctor = $stmt->fetch();

            if (!$doctor) {
                $this->error('Invalid access code', 401);
            }

            // Sprawdź czy system kolejkowy jest włączony
            $stmt = $this->db->prepare("
                SELECT is_enabled FROM queue_config WHERE client_id = ?
            ");
            $stmt->execute([$doctor['client_id']]);
            $queueEnabled = $stmt->fetchColumn();

            if (!$queueEnabled) {
                $this->error('Queue system is disabled', 403);
            }

            // Pobierz dostępne sale dla tego lekarza
            $stmt = $this->db->prepare("
                SELECT id, name, room_number, description
                FROM queue_rooms 
                WHERE client_id = ? AND active = 1
                ORDER BY room_number, name
            ");
            $stmt->execute([$doctor['client_id']]);
            $availableRooms = $stmt->fetchAll();

            $this->success([
                'doctor' => [
                    'id' => $doctor['id'],
                    'first_name' => $doctor['first_name'],
                    'last_name' => $doctor['last_name'],
                    'specialization' => $doctor['specialization'],
                    'client_id' => $doctor['client_id'],
                    'client_name' => $doctor['company_name'],
                    'default_room' => $doctor['room_id'] ? [
                        'id' => $doctor['room_id'],
                        'name' => $doctor['room_name']
                    ] : null
                ],
                'available_rooms' => $availableRooms
            ], 'Login successful');
        } catch (Exception $e) {
            $this->log('Doctor login error: ' . $e->getMessage(), 'ERROR');
            $this->error('Login failed', 500);
        }
    }

    /**
     * Pobierz wizyty dla sali
     */
    public function appointments($roomId) {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;

            if (!$doctorId) {
                $this->error('Doctor ID required', 400);
            }

            // Sprawdź czy lekarz ma dostęp do tej sali
            $stmt = $this->db->prepare("
                SELECT qr.*, d.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);
            $room = $stmt->fetch();

            if (!$room) {
                $this->error('Room not found or access denied', 403);
            }

            $date = $_GET['date'] ?? date('Y-m-d');

            // Pobierz aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments 
                WHERE room_id = ? AND status = 'current'
                ORDER BY id DESC LIMIT 1
            ");
            $stmt->execute([$roomId]);
            $current = $stmt->fetch();

            // Pobierz oczekujące wizyty
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE room_id = ? AND status = 'waiting'
                AND date(appointment_date) = ?
                ORDER BY appointment_time ASC
            ");
            $stmt->execute([$roomId, $date]);
            $waiting = $stmt->fetchAll();

            // Pobierz zakończone wizyty
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE room_id = ? AND status = 'completed'
                AND date(appointment_date) = ?
                ORDER BY appointment_time ASC
            ");
            $stmt->execute([$roomId, $date]);
            $completed = $stmt->fetchAll();

            $this->success([
                'room' => [
                    'id' => $room['id'],
                    'name' => $room['name'],
                    'room_number' => $room['room_number']
                ],
                'current' => $current,
                'waiting' => $waiting,
                'completed' => $completed,
                'date' => $date
            ]);
        } catch (Exception $e) {
            $this->log('Error getting appointments: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get appointments', 500);
        }
    }

    /**
     * Wywołaj następną wizytę
     */
    public function callNext($roomId) {
        try {
            $data = $this->getRequestData();
            $doctorId = $data['doctor_id'] ?? null;

            if (!$doctorId) {
                $this->error('Doctor ID required', 400);
            }

            // Sprawdź dostęp
            $stmt = $this->db->prepare("
                SELECT qr.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);

            if (!$stmt->fetch()) {
                $this->error('Access denied', 403);
            }

            $this->db->beginTransaction();

            try {
                // Oznacz aktualną wizytę jako zakończoną
                $stmt = $this->db->prepare("
                    UPDATE queue_appointments
                    SET status = 'completed', completed_at = datetime('now')
                    WHERE room_id = ? AND status = 'current'
                ");
                $stmt->execute([$roomId]);

                // Znajdź następną wizytę
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE room_id = ? AND status = 'waiting'
                    AND date(appointment_date) = date('now')
                    ORDER BY appointment_time ASC LIMIT 1
                ");
                $stmt->execute([$roomId]);
                $nextAppointment = $stmt->fetch();

                if ($nextAppointment) {
                    // Oznacz jako aktualną
                    $stmt = $this->db->prepare("
                        UPDATE queue_appointments
                        SET status = 'current', called_at = datetime('now')
                        WHERE id = ?
                    ");
                    $stmt->execute([$nextAppointment['id']]);
                }

                $this->db->commit();

                $this->success([
                    'next_appointment' => $nextAppointment,
                    'message' => $nextAppointment ? 'Next appointment called' : 'No more appointments'
                ]);
            } catch (Exception $e) {
                $this->db->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            $this->log('Error calling next appointment: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to call next appointment', 500);
        }
    }

    /**
     * Wróć do poprzedniej wizyty
     */
    public function previous($roomId) {
        try {
            $data = $this->getRequestData();
            $doctorId = $data['doctor_id'] ?? null;

            if (!$doctorId) {
                $this->error('Doctor ID required', 400);
            }

            // Sprawdź dostęp
            $stmt = $this->db->prepare("
                SELECT qr.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);

            if (!$stmt->fetch()) {
                $this->error('Access denied', 403);
            }

            $this->db->beginTransaction();

            try {
                // Znajdź ostatnią zakończoną wizytę
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE room_id = ? AND status = 'completed'
                    AND date(appointment_date) = date('now')
                    ORDER BY completed_at DESC LIMIT 1
                ");
                $stmt->execute([$roomId]);
                $lastCompleted = $stmt->fetch();

                if (!$lastCompleted) {
                    $this->error('No previous appointment found', 404);
                }

                // Oznacz aktualną wizytę jako oczekującą
                $stmt = $this->db->prepare("
                    UPDATE queue_appointments
                    SET status = 'waiting'
                    WHERE room_id = ? AND status = 'current'
                ");
                $stmt->execute([$roomId]);

                // Przywróć poprzednią wizytę jako aktualną
                $stmt = $this->db->prepare("
                    UPDATE queue_appointments
                    SET status = 'current', called_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$lastCompleted['id']]);

                $this->db->commit();

                $this->success([
                    'previous_appointment' => $lastCompleted,
                    'message' => 'Previous appointment restored'
                ]);
            } catch (Exception $e) {
                $this->db->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            $this->log('Error going to previous appointment: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to go to previous appointment', 500);
        }
    }

    /**
     * Pomiń aktualną wizytę
     */
    public function skipCurrent($roomId) {
        try {
            $data = $this->getRequestData();
            $doctorId = $data['doctor_id'] ?? null;

            if (!$doctorId) {
                $this->error('Doctor ID required', 400);
            }

            // Sprawdź dostęp
            $stmt = $this->db->prepare("
                SELECT qr.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);

            if (!$stmt->fetch()) {
                $this->error('Access denied', 403);
            }

            $this->db->beginTransaction();

            try {
                // Oznacz aktualną wizytę jako pominiętą
                $stmt = $this->db->prepare("
                    UPDATE queue_appointments
                    SET status = 'skipped'
                    WHERE room_id = ? AND status = 'current'
                ");
                $stmt->execute([$roomId]);

                // Znajdź następną wizytę
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE room_id = ? AND status = 'waiting'
                    AND date(appointment_date) = date('now')
                    ORDER BY appointment_time ASC LIMIT 1
                ");
                $stmt->execute([$roomId]);
                $nextAppointment = $stmt->fetch();

                if ($nextAppointment) {
                    // Oznacz jako aktualną
                    $stmt = $this->db->prepare("
                        UPDATE queue_appointments
                        SET status = 'current', called_at = datetime('now')
                        WHERE id = ?
                    ");
                    $stmt->execute([$nextAppointment['id']]);
                }

                $this->db->commit();

                $this->success([
                    'next_appointment' => $nextAppointment,
                    'message' => 'Current appointment skipped'
                ]);
            } catch (Exception $e) {
                $this->db->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            $this->log('Error skipping appointment: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to skip appointment', 500);
        }
    }

    /**
     * Pobierz statystyki dla sali
     */
    public function stats($roomId) {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;

            if (!$doctorId) {
                $this->error('Doctor ID required', 400);
            }

            // Sprawdź dostęp
            $stmt = $this->db->prepare("
                SELECT qr.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);

            if (!$stmt->fetch()) {
                $this->error('Access denied', 403);
            }

            $date = $_GET['date'] ?? date('Y-m-d');

            // Statystyki dla dnia
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting,
                    SUM(CASE WHEN status = 'current' THEN 1 ELSE 0 END) as current,
                    SUM(CASE WHEN status = 'skipped' THEN 1 ELSE 0 END) as skipped
                FROM queue_appointments 
                WHERE room_id = ? AND date(appointment_date) = ?
            ");
            $stmt->execute([$roomId, $date]);
            $stats = $stmt->fetch();

            $this->success([
                'date' => $date,
                'stats' => $stats
            ]);
        } catch (Exception $e) {
            $this->log('Error getting stats: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get stats', 500);
        }
    }

    /**
     * Sprawdź dostępność sali
     */
    public function checkRoomAvailability() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'room_id']);

            $doctorId = $data['doctor_id'];
            $roomId = $data['room_id'];
            $date = $data['date'] ?? date('Y-m-d');

            // Sprawdź czy lekarz ma dostęp do sali
            $stmt = $this->db->prepare("
                SELECT qr.*, d.client_id
                FROM queue_rooms qr
                JOIN queue_doctors d ON qr.client_id = d.client_id
                WHERE qr.id = ? AND d.id = ? AND qr.active = 1 AND d.active = 1
            ");
            $stmt->execute([$roomId, $doctorId]);
            $room = $stmt->fetch();

            if (!$room) {
                $this->error('Room not available', 403);
            }

            $this->success([
                'available' => true,
                'room' => [
                    'id' => $room['id'],
                    'name' => $room['name'],
                    'room_number' => $room['room_number']
                ],
                'date' => $date
            ]);
        } catch (Exception $e) {
            $this->log('Error checking room availability: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to check room availability', 500);
        }
    }
}
