<?php

class ImportApiController extends ApiController {

    /**
     * Import danych z Chrome Extension
     */
    public function import() {
        try {
            $data = $this->getRequestData();

            // Debug logowanie
            $this->log('Import request received. Raw data: ' . json_encode($data), 'DEBUG');
            $this->log('Import request keys: ' . implode(', ', array_keys($data)), 'DEBUG');

            $this->validateRequired($data, ['sync_code', 'data']);

            $syncCode = $data['sync_code'];
            $importData = $data['data'];

            // Sprawdź format danych - czy to format z rozszerzenia Chrome czy standardowy
            if (isset($importData['days']) && is_array($importData['days'])) {
                // Format z rozszerzenia Chrome - przekształć na standardowy format
                $this->log('Processing Chrome extension format data', 'DEBUG');
                $standardData = $this->convertChromeExtensionData($importData);
                $importData = $standardData;
            }

            // Znajdź ustawienia importu
            $stmt = $this->db->prepare("
                SELECT i.*, u.id as user_id, u.company_name
                FROM import_settings i
                JOIN users u ON i.client_id = u.id
                WHERE i.sync_code = ? AND i.is_active = 1
            ");
            $stmt->execute([$syncCode]);
            $importSettings = $stmt->fetch();

            if (!$importSettings) {
                $this->error('Invalid sync code or import settings not found', 404);
            }

            // Rozpocznij log synchronizacji
            $stmt = $this->db->prepare("
                INSERT INTO sync_logs (import_setting_id, sync_type, status, started_at)
                VALUES (?, 'manual', 'processing', datetime('now'))
            ");
            $stmt->execute([$importSettings['id']]);
            $logId = $this->db->lastInsertId();

            $this->db->beginTransaction();

            try {
                $processedCount = 0;
                $updatedCount = 0;
                $createdCount = 0;
                $errors = [];

                foreach ($importData as $record) {
                    try {
                        $result = $this->processRecord($record, $importSettings);
                        $processedCount++;

                        if ($result['action'] === 'created') {
                            $createdCount++;
                        } elseif ($result['action'] === 'updated') {
                            $updatedCount++;
                        }
                    } catch (Exception $e) {
                        $errors[] = [
                            'record' => $record,
                            'error' => $e->getMessage()
                        ];
                    }
                }

                // Zaktualizuj log
                $status = empty($errors) ? 'success' : (count($errors) < count($importData) ? 'partial' : 'error');
                $errorMessage = empty($errors) ? null : json_encode($errors);

                $stmt = $this->db->prepare("
                    UPDATE sync_logs 
                    SET status = ?, records_processed = ?, records_updated = ?, 
                        records_created = ?, error_message = ?, completed_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([
                    $status,
                    $processedCount,
                    $updatedCount,
                    $createdCount,
                    $errorMessage,
                    $logId
                ]);

                $this->db->commit();

                // Przetwórz dane na wizyty w systemie kolejkowym
                $appointmentsProcessed = $this->processAppointmentsFromSyncData($importSettings);

                // Aktualizuj last_sync w import_settings
                $stmt = $this->db->prepare("
                    UPDATE import_settings
                    SET last_sync = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$importSettings['id']]);

                $this->success([
                    'import_id' => $logId,
                    'status' => $status,
                    'processed' => $processedCount,
                    'created' => $createdCount,
                    'updated' => $updatedCount,
                    'errors' => count($errors),
                    'error_details' => Config::isDebug() ? $errors : null,
                    'appointments_processed' => $appointmentsProcessed
                ], 'Import completed');
            } catch (Exception $e) {
                $this->db->rollBack();

                // Zaktualizuj log z błędem
                $stmt = $this->db->prepare("
                    UPDATE sync_logs 
                    SET status = 'error', error_message = ?, completed_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$e->getMessage(), $logId]);

                throw $e;
            }
        } catch (Exception $e) {
            $this->log('Import error: ' . $e->getMessage(), 'ERROR');
            $this->error('Import failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Sprawdź status importu
     */
    public function status($syncCode) {
        try {
            // Znajdź ustawienia importu
            $stmt = $this->db->prepare("
                SELECT i.*, u.company_name
                FROM import_settings i
                JOIN users u ON i.client_id = u.id
                WHERE i.sync_code = ? AND i.is_active = 1
            ");
            $stmt->execute([$syncCode]);
            $importSettings = $stmt->fetch();

            if (!$importSettings) {
                $this->error('Invalid sync code', 404);
            }

            // Pobierz ostatnie logi synchronizacji
            $stmt = $this->db->prepare("
                SELECT * FROM sync_logs 
                WHERE import_setting_id = ? 
                ORDER BY started_at DESC 
                LIMIT 10
            ");
            $stmt->execute([$importSettings['id']]);
            $logs = $stmt->fetchAll();

            // Pobierz mapowania lekarzy
            $stmt = $this->db->prepare("
                SELECT * FROM external_doctor_mappings 
                WHERE import_setting_id = ?
                ORDER BY external_doctor_name
            ");
            $stmt->execute([$importSettings['id']]);
            $mappings = $stmt->fetchAll();

            $this->success([
                'import_settings' => [
                    'id' => $importSettings['id'],
                    'name' => $importSettings['name'],
                    'client_name' => $importSettings['company_name'],
                    'is_active' => (bool)$importSettings['is_active'],
                    'last_sync' => $importSettings['last_sync']
                ],
                'recent_logs' => $logs,
                'doctor_mappings' => $mappings
            ]);
        } catch (Exception $e) {
            $this->log('Status check error: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get status', 500);
        }
    }

    /**
     * Automatyczne mapowanie lekarzy
     */
    public function autoMap($syncCode) {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['external_doctors']);

            $externalDoctors = $data['external_doctors'];

            // Znajdź ustawienia importu
            $stmt = $this->db->prepare("
                SELECT * FROM import_settings 
                WHERE sync_code = ? AND is_active = 1
            ");
            $stmt->execute([$syncCode]);
            $importSettings = $stmt->fetch();

            if (!$importSettings) {
                $this->error('Invalid sync code', 404);
            }

            // Pobierz istniejących lekarzy
            $stmt = $this->db->prepare("
                SELECT * FROM queue_doctors 
                WHERE client_id = ? AND active = 1
                ORDER BY first_name, last_name
            ");
            $stmt->execute([$importSettings['client_id']]);
            $localDoctors = $stmt->fetchAll();

            $suggestions = [];

            foreach ($externalDoctors as $externalDoctor) {
                $bestMatch = null;
                $bestScore = 0;

                foreach ($localDoctors as $localDoctor) {
                    $score = $this->calculateSimilarity(
                        $externalDoctor['name'],
                        $localDoctor['first_name'] . ' ' . $localDoctor['last_name']
                    );

                    if ($score > $bestScore && $score > 0.6) { // 60% podobieństwa
                        $bestScore = $score;
                        $bestMatch = $localDoctor;
                    }
                }

                $suggestions[] = [
                    'external_doctor' => $externalDoctor,
                    'suggested_local_doctor' => $bestMatch,
                    'similarity_score' => $bestScore
                ];
            }

            $this->success([
                'suggestions' => $suggestions,
                'local_doctors' => $localDoctors
            ]);
        } catch (Exception $e) {
            $this->log('Auto mapping error: ' . $e->getMessage(), 'ERROR');
            $this->error('Auto mapping failed', 500);
        }
    }

    /**
     * Przetwórz pojedynczy rekord
     */
    private function processRecord($record, $importSettings) {
        // Sprawdź czy rekord już istnieje
        $stmt = $this->db->prepare("
            SELECT * FROM sync_data 
            WHERE import_setting_id = ? AND external_id = ?
        ");
        $stmt->execute([$importSettings['id'], $record['id']]);
        $existingRecord = $stmt->fetch();

        if ($existingRecord) {
            // Aktualizuj istniejący rekord
            $stmt = $this->db->prepare("
                UPDATE sync_data 
                SET data = ?, last_updated = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([json_encode($record), $existingRecord['id']]);

            return ['action' => 'updated', 'id' => $existingRecord['id']];
        } else {
            // Utwórz nowy rekord
            $stmt = $this->db->prepare("
                INSERT INTO sync_data (import_setting_id, external_id, data, created_at, last_updated)
                VALUES (?, ?, ?, datetime('now'), datetime('now'))
            ");
            $stmt->execute([
                $importSettings['id'],
                $record['id'],
                json_encode($record)
            ]);

            return ['action' => 'created', 'id' => $this->db->lastInsertId()];
        }
    }

    /**
     * Oblicz podobieństwo między dwoma stringami
     */
    private function calculateSimilarity($str1, $str2) {
        $str1 = strtolower(trim($str1));
        $str2 = strtolower(trim($str2));

        if ($str1 === $str2) {
            return 1.0;
        }

        // Użyj algorytmu Levenshtein
        $maxLen = max(strlen($str1), strlen($str2));
        if ($maxLen === 0) {
            return 0.0;
        }

        $distance = levenshtein($str1, $str2);
        return 1.0 - ($distance / $maxLen);
    }

    /**
     * Konwertuj dane z rozszerzenia Chrome na standardowy format
     */
    private function convertChromeExtensionData($chromeData) {
        $standardData = [];

        if (!isset($chromeData['days']) || !is_array($chromeData['days'])) {
            return $standardData;
        }

        foreach ($chromeData['days'] as $day) {
            if (!isset($day['doctors']) || !is_array($day['doctors'])) {
                continue;
            }

            foreach ($day['doctors'] as $doctor) {
                if (!isset($doctor['doctorId'])) {
                    continue;
                }

                // Utwórz rekord dla lekarza
                $doctorRecord = [
                    'id' => 'doctor_' . $doctor['doctorId'] . '_' . $day['date'],
                    'type' => 'doctor',
                    'external_doctor_id' => $doctor['doctorId'],
                    'doctor_name' => $doctor['doctorName'] ?? '',
                    'date' => $day['date'],
                    'appointments' => []
                ];

                // Dodaj wizyty jeśli istnieją
                if (isset($doctor['appointments']) && is_array($doctor['appointments'])) {
                    foreach ($doctor['appointments'] as $appointment) {
                        $appointmentRecord = [
                            'id' => 'appointment_' . ($appointment['appointmentId'] ?? uniqid()),
                            'type' => 'appointment',
                            'external_appointment_id' => $appointment['appointmentId'] ?? '',
                            'external_doctor_id' => $doctor['doctorId'],
                            'doctor_name' => $doctor['doctorName'] ?? '',
                            'patient_first_name' => $appointment['patientFirstName'] ?? '',
                            'patient_last_name' => $appointment['patientLastName'] ?? '',
                            'appointment_start' => $appointment['appointmentStart'] ?? '',
                            'appointment_end' => $appointment['appointmentEnd'] ?? '',
                            'appointment_duration' => $appointment['appointmentDuration'] ?? 0,
                            'date' => $day['date']
                        ];

                        $doctorRecord['appointments'][] = $appointmentRecord;
                        $standardData[] = $appointmentRecord;
                    }
                }

                $standardData[] = $doctorRecord;
            }
        }

        $this->log('Converted Chrome extension data: ' . count($standardData) . ' records', 'DEBUG');
        return $standardData;
    }

    /**
     * Przetwórz dane z sync_data na wizyty w systemie kolejkowym
     */
    private function processAppointmentsFromSyncData($importSettings) {
        $processedCount = 0;

        try {
            // Pobierz najnowsze dane z sync_data dla tego importu
            $stmt = $this->db->prepare("
                SELECT * FROM sync_data
                WHERE import_setting_id = ?
                ORDER BY last_updated DESC
            ");
            $stmt->execute([$importSettings['id']]);
            $syncRecords = $stmt->fetchAll();

            foreach ($syncRecords as $syncRecord) {
                $data = json_decode($syncRecord['data'], true);

                if (!$data || !isset($data['type']) || $data['type'] !== 'appointment') {
                    continue;
                }

                // Znajdź mapowanie lekarza
                $doctorMapping = $this->findOrCreateDoctorMapping(
                    $importSettings['id'],
                    $data['external_doctor_id'],
                    $data['doctor_name']
                );

                if (!$doctorMapping || !$doctorMapping['system_doctor_id']) {
                    $this->log('No doctor mapping found for: ' . $data['doctor_name'], 'WARNING');
                    continue;
                }

                // Sprawdź czy wizyta już istnieje
                $existingAppointment = $this->findExistingAppointment(
                    $data['external_appointment_id'],
                    $importSettings['client_id']
                );

                if ($existingAppointment) {
                    // Aktualizuj istniejącą wizytę
                    $this->updateAppointment($existingAppointment['id'], $data, $doctorMapping);
                } else {
                    // Utwórz nową wizytę
                    $this->createAppointment($data, $doctorMapping, $importSettings['client_id']);
                }

                $processedCount++;
            }

            $this->log('Processed appointments from sync data: ' . $processedCount, 'INFO');
        } catch (Exception $e) {
            $this->log('Error processing appointments: ' . $e->getMessage(), 'ERROR');
        }

        return $processedCount;
    }

    /**
     * Znajdź lub utwórz mapowanie lekarza
     */
    private function findOrCreateDoctorMapping($importSettingId, $externalDoctorId, $doctorName) {
        // Sprawdź czy mapowanie już istnieje
        $stmt = $this->db->prepare("
            SELECT * FROM external_doctor_mappings
            WHERE import_setting_id = ? AND external_doctor_id = ?
        ");
        $stmt->execute([$importSettingId, $externalDoctorId]);
        $mapping = $stmt->fetch();

        if ($mapping) {
            // Aktualizuj last_seen
            $stmt = $this->db->prepare("
                UPDATE external_doctor_mappings
                SET last_seen = datetime('now'), external_doctor_name = ?
                WHERE id = ?
            ");
            $stmt->execute([$doctorName, $mapping['id']]);
            return $mapping;
        }

        // Utwórz nowe mapowanie
        $stmt = $this->db->prepare("
            INSERT INTO external_doctor_mappings
            (import_setting_id, external_doctor_id, external_doctor_name, last_seen)
            VALUES (?, ?, ?, datetime('now'))
        ");
        $stmt->execute([$importSettingId, $externalDoctorId, $doctorName]);

        // Pobierz utworzone mapowanie
        $mappingId = $this->db->lastInsertId();
        $stmt = $this->db->prepare("SELECT * FROM external_doctor_mappings WHERE id = ?");
        $stmt->execute([$mappingId]);

        return $stmt->fetch();
    }

    /**
     * Znajdź istniejącą wizytę
     */
    private function findExistingAppointment($externalAppointmentId, $clientId) {
        $stmt = $this->db->prepare("
            SELECT qa.* FROM queue_appointments qa
            WHERE qa.external_id = ? AND qa.client_id = ?
        ");
        $stmt->execute([$externalAppointmentId, $clientId]);
        return $stmt->fetch();
    }

    /**
     * Utwórz nową wizytę
     */
    private function createAppointment($data, $doctorMapping, $clientId) {
        // Znajdź domyślną salę dla lekarza (opcjonalnie)
        $stmt = $this->db->prepare("
            SELECT * FROM queue_rooms
            WHERE doctor_id = ? AND client_id = ? AND active = 1
            LIMIT 1
        ");
        $stmt->execute([$doctorMapping['system_doctor_id'], $clientId]);
        $room = $stmt->fetch();

        // Utwórz wizytę - sala jest opcjonalna
        $stmt = $this->db->prepare("
            INSERT INTO queue_appointments
            (client_id, room_id, doctor_id, external_id, patient_name, appointment_time,
             appointment_date, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'waiting', datetime('now'))
        ");

        $patientName = trim(($data['patient_first_name'] ?? '') . ' ' . ($data['patient_last_name'] ?? ''));
        $appointmentDate = $data['date'] ?? date('Y-m-d');
        $appointmentTime = $data['appointment_start'] ?? '';

        $stmt->execute([
            $clientId,
            $room ? $room['id'] : null, // Sala opcjonalna
            $doctorMapping['system_doctor_id'], // Lekarz obowiązkowy
            $data['external_appointment_id'],
            $patientName,
            $appointmentTime,
            $appointmentDate
        ]);

        $this->log('Created appointment for doctor: ' . $doctorMapping['system_doctor_id'] .
            ($room ? ' in room: ' . $room['name'] : ' (no room assigned)'), 'INFO');

        return $this->db->lastInsertId();
    }

    /**
     * Aktualizuj istniejącą wizytę
     */
    private function updateAppointment($appointmentId, $data, $doctorMapping) {
        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET patient_name = ?, appointment_time = ?, appointment_date = ?
            WHERE id = ?
        ");

        $patientName = trim(($data['patient_first_name'] ?? '') . ' ' . ($data['patient_last_name'] ?? ''));
        $appointmentDate = $data['date'] ?? date('Y-m-d');
        $appointmentTime = $data['appointment_start'] ?? '';

        $stmt->execute([
            $patientName,
            $appointmentTime,
            $appointmentDate,
            $appointmentId
        ]);

        return $stmt->rowCount() > 0;
    }
}
