<?php

class AdsApiController extends ApiController {

    /**
     * Pobierz reklamy dla klienta
     */
    public function getAds($clientId) {
        try {
            // Pobierz aktywne kampanie dla klienta
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id, 
                       c.duration, c.rate_per_second, c.description
                FROM campaign_assignments ca
                JOIN campaigns c ON ca.campaign_id = c.id
                WHERE ca.client_id = ? AND c.is_active = 1 
                      AND c.budget > c.spent
                      AND ca.is_accepted = 1
                ORDER BY RANDOM()
                LIMIT 1
            ");

            $stmt->execute([$clientId]);
            $campaign = $stmt->fetch();

            if (!$campaign) {
                $this->success([
                    'ads' => [],
                    'message' => 'No active campaigns available'
                ]);
                return;
            }

            // Sprawdź limit częstotliwości
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM ad_views
                WHERE campaign_id = ? AND client_id = ?
                AND timestamp > datetime('now', '-1 hour')
            ");
            $stmt->execute([$campaign['id'], $clientId]);
            $recentViews = $stmt->fetchColumn();

            // Pobierz maksymalną częstotliwość z kampanii
            $stmt = $this->db->prepare("
                SELECT max_frequency_per_hour FROM campaigns WHERE id = ?
            ");
            $stmt->execute([$campaign['id']]);
            $maxFrequency = $stmt->fetchColumn();

            if ($maxFrequency > 0 && $recentViews >= $maxFrequency) {
                $this->success([
                    'ads' => [],
                    'message' => 'Frequency limit reached'
                ]);
                return;
            }

            // Przygotuj URL mediów
            if ($campaign['media_type'] === 'image' || $campaign['media_type'] === 'video') {
                $campaign['media_url'] = $this->getFullMediaUrl($campaign['media_url']);
            }

            $this->success([
                'ads' => [$campaign],
                'client_id' => $clientId,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error getting ads: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get ads', 500);
        }
    }

    /**
     * Zapisz wyświetlenie reklamy
     */
    public function recordView() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['campaign_id', 'client_id', 'duration']);

            $campaignId = $data['campaign_id'];
            $clientId = $data['client_id'];
            $duration = (int)$data['duration'];

            // Sprawdź czy kampania istnieje i jest aktywna
            $stmt = $this->db->prepare("
                SELECT c.*, ca.is_accepted 
                FROM campaigns c
                JOIN campaign_assignments ca ON c.id = ca.campaign_id
                WHERE c.id = ? AND ca.client_id = ? AND c.is_active = 1
            ");
            $stmt->execute([$campaignId, $clientId]);
            $campaign = $stmt->fetch();

            if (!$campaign || !$campaign['is_accepted']) {
                $this->error('Campaign not found or not accepted', 404);
            }

            // Oblicz koszt
            $cost = $duration * $campaign['rate_per_second'];

            // Sprawdź czy kampania ma wystarczający budżet
            if ($campaign['spent'] + $cost > $campaign['budget']) {
                $this->error('Campaign budget exceeded', 400);
            }

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            try {
                // Zapisz wyświetlenie
                $stmt = $this->db->prepare("
                    INSERT INTO ad_views (campaign_id, client_id, duration_seconds, cost, timestamp)
                    VALUES (?, ?, ?, ?, datetime('now'))
                ");
                $stmt->execute([$campaignId, $clientId, $duration, $cost]);

                // Zaktualizuj wydane środki w kampanii
                $stmt = $this->db->prepare("
                    UPDATE campaigns 
                    SET spent = spent + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$cost, $campaignId]);

                // Zaktualizuj saldo reklamodawcy (odejmij koszt)
                $stmt = $this->db->prepare("
                    UPDATE users 
                    SET balance = balance - ? 
                    WHERE id = ?
                ");
                $stmt->execute([$cost, $campaign['advertiser_id']]);

                // Zaktualizuj saldo reklamobiorcy (dodaj koszt)
                $stmt = $this->db->prepare("
                    UPDATE users 
                    SET balance = balance + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$cost, $clientId]);

                $this->db->commit();

                $this->success([
                    'view_recorded' => true,
                    'cost' => $cost,
                    'duration' => $duration
                ], 'View recorded successfully');
            } catch (Exception $e) {
                $this->db->rollBack();
                throw $e;
            }
        } catch (Exception $e) {
            $this->log('Error recording view: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record view', 500);
        }
    }

    /**
     * Pobierz pełny URL do pliku multimedialnego
     */
    private function getFullMediaUrl($mediaUrl) {
        if (strpos($mediaUrl, 'http') === 0) {
            return $mediaUrl; // Już pełny URL
        }

        // Względny URL - dodaj bazowy URL
        $baseUrl = Config::getAppUrl();
        return rtrim($baseUrl, '/') . '/' . ltrim($mediaUrl, '/');
    }
}
