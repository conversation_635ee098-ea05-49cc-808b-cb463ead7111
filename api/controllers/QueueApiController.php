<?php

class QueueApiController extends ApiController {

    /**
     * Pobierz status kolejki dla klienta
     */
    public function getQueueStatus($clientId) {
        try {
            // Sprawdź czy klient istnieje i ma włączony system kolejkowy
            $stmt = $this->db->prepare("
                SELECT qc.*, u.company_name 
                FROM queue_config qc 
                JOIN users u ON qc.client_id = u.id 
                WHERE qc.client_id = ? AND qc.is_enabled = 1
            ");
            $stmt->execute([$clientId]);
            $config = $stmt->fetch();

            if (!$config) {
                $this->error('Queue system not found or disabled', 404);
            }

            // Pobierz lekarzy z wizytami na dzisiaj
            $stmt = $this->db->prepare("
                SELECT DISTINCT d.*,
                       r.id as room_id, r.name as room_name, r.room_number
                FROM queue_doctors d
                JOIN queue_appointments qa ON d.id = qa.doctor_id
                LEFT JOIN queue_rooms r ON d.id = r.doctor_id AND r.client_id = ? AND r.active = 1
                WHERE qa.client_id = ?
                  AND date(qa.appointment_date) = date('now')
                ORDER BY d.first_name, d.last_name
            ");
            $stmt->execute([$clientId, $clientId]);
            $doctors = $stmt->fetchAll();

            $doctorsData = [];
            foreach ($doctors as $doctor) {
                // Pobierz aktualny numer
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE doctor_id = ? AND client_id = ? AND status = 'current'
                    AND date(appointment_date) = date('now')
                    ORDER BY id DESC LIMIT 1
                ");
                $stmt->execute([$doctor['id'], $clientId]);
                $current = $stmt->fetch();

                // Pobierz kolejne numery
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE doctor_id = ? AND client_id = ? AND status = 'waiting'
                    AND date(appointment_date) = date('now')
                    ORDER BY appointment_time ASC LIMIT 5
                ");
                $stmt->execute([$doctor['id'], $clientId]);
                $waiting = $stmt->fetchAll();

                $doctorsData[] = [
                    'id' => $doctor['id'],
                    'name' => $doctor['first_name'] . ' ' . $doctor['last_name'],
                    'specialization' => $doctor['specialization'],
                    'photo_url' => $doctor['photo_url'],
                    'room' => $doctor['room_id'] ? [
                        'id' => $doctor['room_id'],
                        'name' => $doctor['room_name'],
                        'room_number' => $doctor['room_number']
                    ] : null,
                    'current' => $current ? [
                        'id' => $current['id'],
                        'number' => $current['appointment_time'],
                        'patient_name' => $current['patient_name']
                    ] : null,
                    'waiting' => array_map(function ($appointment) {
                        return [
                            'id' => $appointment['id'],
                            'number' => $appointment['appointment_time'],
                            'patient_name' => $appointment['patient_name']
                        ];
                    }, $waiting)
                ];
            }

            $this->success([
                'client' => [
                    'id' => $config['client_id'],
                    'name' => $config['company_name']
                ],
                'doctors' => $doctorsData,
                'last_updated' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error getting queue status: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get queue status', 500);
        }
    }

    /**
     * Pobierz zmiany w kolejce (dla real-time updates)
     */
    public function getQueueChanges($clientId) {
        try {
            $lastUpdate = $_GET['since'] ?? null;

            if (!$lastUpdate) {
                $this->error('Missing "since" parameter', 400);
            }

            // Sprawdź czy klient istnieje
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_config 
                WHERE client_id = ? AND is_enabled = 1
            ");
            $stmt->execute([$clientId]);

            if ($stmt->fetchColumn() == 0) {
                $this->error('Queue system not found or disabled', 404);
            }

            // Pobierz zmiany od ostatniej aktualizacji
            $stmt = $this->db->prepare("
                SELECT qa.*, qr.name as room_name, qr.room_number
                FROM queue_appointments qa
                JOIN queue_rooms qr ON qa.room_id = qr.id
                WHERE qr.client_id = ?
                AND qa.created_at > ?
                AND date(qa.appointment_date) = date('now')
                ORDER BY qa.created_at ASC
            ");
            $stmt->execute([$clientId, $lastUpdate]);
            $changes = $stmt->fetchAll();

            $this->success([
                'changes' => $changes,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error getting queue changes: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get queue changes', 500);
        }
    }
}
