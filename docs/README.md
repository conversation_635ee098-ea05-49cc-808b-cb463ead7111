# 📋 KtoOstatni - Dokumentacja Systemu

## 🎯 **Przegląd Systemu**

KtoOstatni to system kolejkowy dla przychodni medycznych z automatyczną synchronizacją z systemem iGabinet.

### **Główne komponenty:**
- 🏥 **System kolejkowy** - zarządzanie wizytami i kolejkami
- 🔄 **Synchronizacja iGabinet** - automatyczny import wizyt
- 🌐 **Rozszerzenie Chrome** - pobieranie danych z iGabinet
- 📱 **PWA** - aplikacja mobilna dla pacjentów

## 📖 **Dokumentacja według kategorii:**

### **🏥 System Kolejkowy**
- [System Kolejkowy - Przegląd](./01-QUEUE-SYSTEM.md) - Główne funkcje systemu kolejkowego
- [Zarządzanie Lekarzami](./02-DOCTORS-MANAGEMENT.md) - Dodawanie i konfiguracja lekarzy
- [Zarządzanie Wizytami](./03-APPOINTMENTS-MANAGEMENT.md) - Obsługa wizyt i kolejek

### **🔄 Import i Synchronizacja**
- [System Importu - Przegląd](./04-IMPORT-SYSTEM.md) - Ogólny opis systemu importu
- [Synchronizacja z iGabinet](./05-IGABINET-SYNC.md) - Konfiguracja i zasady synchronizacji
- [Mapowanie Lekarzy](./06-DOCTOR-MAPPING.md) - Mapowanie lekarzy między systemami

### **🌐 Rozszerzenie Chrome**
- [Instalacja Rozszerzenia](./07-CHROME-EXTENSION-INSTALL.md) - Instrukcja instalacji
- [Konfiguracja Rozszerzenia](./08-CHROME-EXTENSION-CONFIG.md) - Ustawienia i konfiguracja
- [Rozwiązywanie Problemów](./09-CHROME-EXTENSION-TROUBLESHOOTING.md) - Debugowanie i błędy

### **🛠 Administracja**
- [Konfiguracja Systemu](./10-SYSTEM-CONFIG.md) - Ustawienia systemowe
- [Baza Danych](./11-DATABASE.md) - Struktura i zarządzanie bazą
- [API Dokumentacja](./12-API-REFERENCE.md) - Dokumentacja API

### **📱 PWA i Frontend**
- [Aplikacja PWA](./13-PWA-APP.md) - Aplikacja mobilna dla pacjentów
- [Interface Użytkownika](./14-USER-INTERFACE.md) - Opis interfejsów

### **🔧 Rozwój i Utrzymanie**
- [Przewodnik Developera](./15-DEVELOPER-GUIDE.md) - Informacje dla programistów
- [Changelog](./16-CHANGELOG.md) - Historia zmian
- [FAQ](./17-FAQ.md) - Często zadawane pytania

## 🚀 **Szybki Start**

1. **Instalacja systemu:** Zobacz [Konfiguracja Systemu](./10-SYSTEM-CONFIG.md)
2. **Dodanie lekarzy:** Zobacz [Zarządzanie Lekarzami](./02-DOCTORS-MANAGEMENT.md)
3. **Konfiguracja importu:** Zobacz [System Importu](./04-IMPORT-SYSTEM.md)
4. **Instalacja rozszerzenia:** Zobacz [Instalacja Rozszerzenia](./07-CHROME-EXTENSION-INSTALL.md)

## 📞 **Wsparcie**

W przypadku problemów sprawdź:
1. [FAQ](./17-FAQ.md) - najczęstsze problemy
2. [Rozwiązywanie Problemów](./09-CHROME-EXTENSION-TROUBLESHOOTING.md) - problemy z rozszerzeniem
3. [Przewodnik Developera](./15-DEVELOPER-GUIDE.md) - problemy techniczne

## 🏗 **Wymagania Systemowe**

- PHP 8.0+
- SQLite 3
- Serwer web (Apache/Nginx)
- Nowoczesna przeglądarka z obsługą JavaScript

## 📁 **Struktura Projektu**

```
ktoostatni/
├── admin/              # Panel administracyjny
│   ├── controllers/    # Kontrolery
│   ├── models/        # Modele danych
│   ├── views/         # Widoki
│   └── core/          # Klasy podstawowe
├── chrome1/           # Rozszerzenie Chrome
├── database/          # Baza danych SQLite
├── docs/             # Dokumentacja
├── pwa/              # Aplikacja PWA
└── uploads/          # Pliki przesłane
```

---
*Dokumentacja systemu KtoOstatni v2.2.0*
*Ostatnia aktualizacja: 2025-08-27*
