# API Dokumentacja - KtoOstatni

## Wprowadzenie

API systemu KtoOstatni zapewnia programowy dostęp do wszystkich funkcji systemu kolejkowego i reklamowego. API jest zorganizowane w RESTful style i zwraca dane w formacie JSON.

## Bazowy URL

```
/api/
```

## Autoryzacja

Większość endpointów wymaga autoryzacji. System używa sesji PHP do zarządzania autoryzacją.

### Logowanie lekarza (PWA)
```http
POST /api/doctor/login
Content-Type: application/json

{
  "access_code": "123456"
}
```

**Odpowiedź:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "doctor": {
      "id": 1,
      "first_name": "Jan",
      "last_name": "<PERSON>wal<PERSON>",
      "specialization": "Kardiolog",
      "client_id": 1,
      "client_name": "Przychodnia ABC",
      "default_room": {
        "id": 1,
        "name": "Gabinet 1"
      }
    },
    "available_rooms": [
      {
        "id": 1,
        "name": "Gabinet 1",
        "room_number": "101",
        "description": "Kardiologia"
      }
    ]
  }
}
```

## Queue API

### Pobierz status kolejki

```http
GET /api/queue/{clientId}
```

**Parametry:**
- `clientId` - ID klienta

**Odpowiedź:**
```json
{
  "success": true,
  "data": {
    "client": {
      "id": 1,
      "name": "Przychodnia ABC"
    },
    "rooms": [
      {
        "id": 1,
        "name": "Gabinet 1",
        "room_number": "101",
        "description": "Kardiologia",
        "doctor": {
          "name": "Dr Jan Kowalski",
          "specialization": "Kardiolog"
        },
        "current": {
          "id": 123,
          "number": 15,
          "patient_name": "Jan Nowak"
        },
        "waiting": [
          {
            "id": 124,
            "number": 16,
            "patient_name": "Anna Kowalska"
          }
        ]
      }
    ],
    "last_updated": "2024-01-15T10:30:00Z"
  }
}
```

### Pobierz zmiany w kolejce

```http
GET /api/queue/{clientId}/changes?since=2024-01-15T10:30:00Z
```

**Parametry:**
- `clientId` - ID klienta
- `since` - Timestamp ostatniej aktualizacji

## Doctor API

### Pobierz wizyty dla sali

```http
GET /api/doctor/appointments/{roomId}?doctor_id=1&date=2024-01-15
```

**Parametry:**
- `roomId` - ID sali
- `doctor_id` - ID lekarza (query parameter)
- `date` - Data (opcjonalnie, domyślnie dzisiaj)

### Wywołaj następną wizytę

```http
POST /api/doctor/call-next/{roomId}
Content-Type: application/json

{
  "doctor_id": 1
}
```

### Wróć do poprzedniej wizyty

```http
POST /api/doctor/previous/{roomId}
Content-Type: application/json

{
  "doctor_id": 1
}
```

### Pomiń aktualną wizytę

```http
POST /api/doctor/skip-current/{roomId}
Content-Type: application/json

{
  "doctor_id": 1
}
```

### Pobierz statystyki sali

```http
GET /api/doctor/stats/{roomId}?doctor_id=1&date=2024-01-15
```

## Ads API

### Pobierz reklamy dla klienta

```http
GET /api/ads/{clientId}
```

**Odpowiedź:**
```json
{
  "success": true,
  "data": {
    "ads": [
      {
        "id": 1,
        "name": "Kampania testowa",
        "media_type": "image",
        "media_url": "/uploads/campaigns/image.jpg",
        "duration": 30,
        "description": "Opis kampanii"
      }
    ],
    "client_id": 1,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Zapisz wyświetlenie reklamy

```http
POST /api/ads/view
Content-Type: application/json

{
  "campaign_id": 1,
  "client_id": 1,
  "duration": 30
}
```

## Import API

### Import danych z Chrome Extension

```http
POST /api/import
Content-Type: application/json

{
  "sync_code": "ABC123",
  "data": [
    {
      "id": "ext_123",
      "doctor_name": "Dr Jan Kowalski",
      "appointment_time": "2024-01-15T10:00:00Z",
      "patient_name": "Jan Nowak"
    }
  ]
}
```

### Sprawdź status importu

```http
GET /api/import/status/{syncCode}
```

### Automatyczne mapowanie lekarzy

```http
POST /api/import/auto-map/{syncCode}
Content-Type: application/json

{
  "external_doctors": [
    {
      "id": "ext_123",
      "name": "Dr Jan Kowalski"
    }
  ]
}
```

## Display API

### Pobierz dane dla wyświetlacza

```http
GET /api/display/{code}
```

**Parametry:**
- `code` - Kod wyświetlacza

### Heartbeat wyświetlacza

```http
POST /api/display/{code}/heartbeat
```

## Obsługa błędów

API zwraca standardowe kody HTTP i strukturę błędów:

```json
{
  "error": true,
  "code": 404,
  "message": "Resource not found",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Kody błędów

- `400` - Bad Request (nieprawidłowe dane)
- `401` - Unauthorized (brak autoryzacji)
- `403` - Forbidden (brak uprawnień)
- `404` - Not Found (zasób nie znaleziony)
- `422` - Unprocessable Entity (błąd walidacji)
- `500` - Internal Server Error (błąd serwera)

## Rate Limiting

API implementuje podstawowe rate limiting (przygotowane do rozszerzenia):
- Domyślnie: 100 żądań na godzinę na IP
- Można konfigurować per endpoint

## Przykłady użycia

### JavaScript (PWA)

```javascript
// Logowanie lekarza
const response = await fetch('/api/doctor/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    access_code: '123456'
  })
});

const data = await response.json();
if (data.success) {
  console.log('Zalogowano:', data.data.doctor);
}
```

### Chrome Extension

```javascript
// Import danych
chrome.runtime.sendMessage({
  action: 'syncData',
  data: appointmentsData
}, (response) => {
  if (response.success) {
    console.log('Synchronizacja zakończona');
  }
});
```
