# 🔄 System Importu - Przegląd

## 🎯 **Cel systemu**

System importu automatycznie synchronizuje dane wizyt z systemu iGabinet do systemu kolejkowego KtoOstatni.

## 🏗 **Architektura systemu**

```
iGabinet → Rozszerzenie Chrome → API KtoOstatni → Baza danych → Frontend
```

### **Komponenty:**
1. **Rozszerzenie Chrome** - pobiera dane z iGabinet
2. **API Import** - przetwarza i zapisuje dane
3. **Mapowanie lekarzy** - łączy lekarzy między systemami
4. **Synchronizacja** - aktualizuje, dodaje i usuwa wizyty

## 🔧 **Proces importu**

### **1. Pobieranie danych (Chrome Extension)**
- Logowanie do iGabinet
- Pobieranie słownika lekarzy
- Ekstrakcja wizyt z kalendarza
- Formatowanie do JSON

### **2. Przesyła<PERSON> danych**
```javascript
// Struktura JSON
{
  "exportDate": "2025-08-27T14:00:00.000Z",
  "syncCode": "igab1234567890123",
  "syncData": {
    "days": [{
      "date": "2025-08-27",
      "doctors": [{
        "doctorId": "114",
        "doctorName": "ginekolog dr Ewelina Sądaj",
        "appointments": [{
          "appointmentId": "12345",
          "patientFirstName": "Anna",
          "patientLastName": "Kowalska",
          "appointmentStart": "09:00",
          "appointmentEnd": "09:30",
          "appointmentDuration": 30
        }]
      }]
    }]
  }
}
```

### **3. Przetwarzanie (API)**
- Walidacja danych
- Mapowanie lekarzy
- Synchronizacja wizyt:
  - **Dodawanie** nowych wizyt
  - **Aktualizowanie** istniejących
  - **Usuwanie** nieaktualnych

## 🗄 **Konfiguracja importu**

### **Ustawienia synchronizacji:**
```sql
-- Tabela: import_settings
INSERT INTO import_settings (
  sync_code,           -- Kod identyfikujący źródło
  system_name,         -- Nazwa systemu (np. "iGabinet")
  client_id,           -- ID klienta w systemie
  is_active            -- Czy import jest aktywny
) VALUES (
  'igab1234567890123',
  'iGabinet Sonokard',
  2,
  1
);
```

### **Mapowanie lekarzy:**
```sql
-- Tabela: external_doctor_mappings
INSERT INTO external_doctor_mappings (
  import_setting_id,   -- ID ustawienia importu
  external_doctor_id,  -- ID lekarza w iGabinet
  external_doctor_name,-- Nazwa lekarza w iGabinet
  system_doctor_id,    -- ID lekarza w KtoOstatni
  is_mapped           -- Czy mapowanie jest aktywne
) VALUES (
  1,
  '114',
  'ginekolog dr Ewelina Sądaj',
  17,
  1
);
```

## 📊 **Logowanie i monitoring**

### **Logi synchronizacji:**
```sql
-- Tabela: sync_logs
SELECT 
  status,              -- success/error
  records_processed,   -- Liczba przetworzonych wizyt
  records_created,     -- Liczba utworzonych wizyt
  records_updated,     -- Liczba zaktualizowanych wizyt
  records_deleted,     -- Liczba usuniętych wizyt
  started_at,          -- Czas rozpoczęcia
  completed_at,        -- Czas zakończenia
  error_message        -- Komunikat błędu (jeśli wystąpił)
FROM sync_logs 
ORDER BY started_at DESC;
```

### **Pliki synchronizacji:**
- Lokalizacja: `admin/data/sync/{client_id}/`
- Format: `YYYY-MM-DD_HH-MM-SS.json`
- Zawartość: Pełne dane JSON z importu

## 🔄 **Zasady synchronizacji**

### **Identyfikacja wizyt:**
- Każda wizyta ma unikalny `external_id` z iGabinet
- Wizyty bez `external_id` to dane lokalne (nie są usuwane)

### **Operacje:**
1. **CREATE** - nowa wizyta z iGabinet
2. **UPDATE** - zmiana istniejącej wizyty
3. **DELETE** - wizyta usunięta z iGabinet

### **Bezpieczeństwo:**
- Usuwane są tylko wizyty z `external_id`
- Wizyty lokalne są chronione
- Transakcje bazodanowe zapewniają spójność

## 🚨 **Obsługa błędów**

### **Typowe problemy:**
- Brak mapowania lekarza → wizyta odrzucona
- Nieprawidłowy format daty → błąd walidacji
- Duplikat `external_id` → aktualizacja zamiast tworzenia

### **Monitoring:**
- Logi w bazie danych
- Pliki JSON dla debugowania
- Powiadomienia o błędach

## ⚙️ **API Endpoints**

### **Import wizyt:**
```
POST /admin/api/import
Content-Type: application/json

{
  "exportDate": "...",
  "syncCode": "...",
  "syncData": {...}
}
```

### **Status synchronizacji:**
```
GET /admin/api/sync-status
Response: {
  "last_sync": "2025-08-27 14:00:00",
  "status": "success",
  "records_processed": 56
}
```

---
*Zobacz także: [Synchronizacja z iGabinet](./05-IGABINET-SYNC.md)*
