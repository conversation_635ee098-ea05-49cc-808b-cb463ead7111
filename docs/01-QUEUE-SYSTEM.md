# 🏥 System Kolejkowy - Przegląd

## 🎯 **Cel systemu**

System kolejkowy KtoOstatni umożliwia pacjentom sprawdzenie swojej pozycji w kolejce oraz aktualnego stanu wizyt w przychodni.

## 🔧 **Główne funkcje**

### **1. Wyświetlanie kolejki**
- Lista pacjentów w kolejce do każdego lekarza
- Aktualna pozycja pacjenta
- Szacowany czas oczekiwania
- Status wizyty (oczekuje, w trakcie, zakończona)

### **2. Informacje o lekarzach**
- Lista dostępnych lekarzy
- Specjalizacje
- Zdjęcia lekarzy
- Status dostępności

### **3. Automatyczne odświeżanie**
- Dane odświeżane co 30 sekund
- Powiadomienia o zmianach
- Synchronizacja z systemem iGabinet

## 📱 **Interfejsy użytkownika**

### **Strona główna (`index.php`)**
- Przegląd wszystkich lekarzy
- Liczba oczekujących pacjentów
- Ostatnia aktualizacja danych

### **Widok lekarza (`doctor.php`)**
- Szczegółowa kolejka dla konkretnego lekarza
- Informacje o pacjentach
- Historia wizyt

### **Panel administracyjny (`admin/`)**
- Zarządzanie lekarzami
- Konfiguracja systemu
- Statystyki i raporty

## 🔄 **Przepływ danych**

1. **Import z iGabinet** → Rozszerzenie Chrome pobiera dane
2. **Synchronizacja** → API importuje wizyty do bazy
3. **Wyświetlanie** → Frontend pokazuje aktualne kolejki
4. **Odświeżanie** → Automatyczna aktualizacja co 30s

## 🛠 **Konfiguracja**

### **Ustawienia podstawowe:**
```php
// admin/config.php
define('REFRESH_INTERVAL', 30);    // Interwał odświeżania (sekundy)
define('MAX_QUEUE_SIZE', 50);      // Maksymalna wielkość kolejki
define('SHOW_PATIENT_NAMES', true); // Pokazuj imiona pacjentów
```

### **Personalizacja:**
- Logo przychodni
- Kolory interfejsu
- Komunikaty dla pacjentów
- Język interfejsu

## 📊 **Statystyki**

System zbiera dane o:
- Liczbie wizyt dziennie
- Średnim czasie oczekiwania
- Najpopularniejszych lekarzach
- Godzinach szczytu

## 🔒 **Bezpieczeństwo**

- Dane pacjentów są anonimizowane
- Brak przechowywania danych osobowych
- Szyfrowane połączenia HTTPS
- Regularne czyszczenie starych danych

---
*Zobacz także: [Zarządzanie Lekarzami](./02-DOCTORS-MANAGEMENT.md)*
