# 🌐 Instalacja Rozszerzenia Chrome

## 🎯 **Cel rozszerzenia**

Rozszerzenie Chrome automatycznie pobiera dane wizyt z systemu iGabinet i przesyła je do systemu KtoOstatni.

## 📦 **Wymagania**

- Google Chrome 88+
- Dostęp do systemu iGabinet
- Uprawnienia administratora w Chrome

## 🔧 **Instalacja krok po kroku**

### **1. Przygotowanie plików**
```bash
# Struktura rozszerzenia
chrome1/
├── manifest.json      # Konfiguracja rozszerzenia
├── content.js        # Główna logika
├── popup.html        # Interface popup
├── popup.js          # Logika popup
├── background.js     # Skrypty w tle
└── icons/           # Ikony rozszerzenia
```

### **2. Włączenie trybu developera**
1. Otwórz Chrome
2. Przejdź do `chrome://extensions/`
3. <PERSON><PERSON><PERSON><PERSON> **"Tryb programisty"** (Developer mode)

### **3. Załadowanie rozszerzenia**
1. <PERSON><PERSON><PERSON>j **"Załaduj rozpakowane"** (Load unpacked)
2. Wybierz folder `chrome1/`
3. Rozszerzenie pojawi się na liście

### **4. Przypięcie do paska**
1. Kliknij ikonę rozszerzeń w Chrome
2. Znajdź "KtoOstatni iGabinet Sync"
3. Kliknij ikonę pinezki 📌

## ⚙️ **Konfiguracja**

### **1. Ustawienia podstawowe**
Po instalacji kliknij ikonę rozszerzenia i ustaw:

- **URL systemu KtoOstatni:** `https://twoja-domena.pl`
- **Kod synchronizacji:** `igab1234567890123`
- **Interwał synchronizacji:** `300` sekund (5 minut)

### **2. Uprawnienia**
Rozszerzenie wymaga dostępu do:
- `https://igabinet.pl/*` - pobieranie danych
- `https://twoja-domena.pl/*` - wysyłanie danych
- `storage` - zapisywanie ustawień
- `activeTab` - dostęp do aktywnej karty

## 🚀 **Pierwsze uruchomienie**

### **1. Test połączenia**
1. Zaloguj się do iGabinet
2. Przejdź do kalendarza wizyt
3. Kliknij ikonę rozszerzenia
4. Kliknij **"Test połączenia"**

### **2. Pierwszy import**
1. Kliknij **"Pobierz dane testowe"**
2. Sprawdź logi w konsoli (F12)
3. Zweryfikuj dane w systemie KtoOstatni

### **3. Automatyczna synchronizacja**
1. Włącz **"Auto-sync"** w popup
2. Rozszerzenie będzie działać w tle
3. Sprawdzaj logi synchronizacji

## 🔍 **Weryfikacja instalacji**

### **Sprawdź czy rozszerzenie:**
- ✅ Pojawia się na liście rozszerzeń
- ✅ Ma aktywną ikonę w pasku
- ✅ Otwiera popup po kliknięciu
- ✅ Łączy się z iGabinet
- ✅ Wysyła dane do KtoOstatni

### **Sprawdź logi:**
```javascript
// Otwórz konsolę (F12) na stronie iGabinet
// Szukaj komunikatów:
"KtoOstatni: Rozszerzenie załadowane"
"KtoOstatni: Pobrano słownik lekarzy: X"
"KtoOstatni: Wysłano dane do API"
```

## 🔄 **Aktualizacja rozszerzenia**

### **Automatyczna (w przyszłości):**
- Rozszerzenie sprawdza aktualizacje
- Automatyczne pobieranie nowych wersji

### **Manualna (obecnie):**
1. Pobierz nowe pliki rozszerzenia
2. Zastąp stare pliki w folderze `chrome1/`
3. Kliknij **"Odśwież"** na `chrome://extensions/`

## 🚨 **Rozwiązywanie problemów**

### **Rozszerzenie nie ładuje się:**
- Sprawdź czy wszystkie pliki są w folderze
- Zweryfikuj `manifest.json`
- Sprawdź logi błędów w Chrome

### **Brak ikony w pasku:**
- Kliknij ikonę rozszerzeń
- Przypnij rozszerzenie
- Sprawdź czy jest włączone

### **Nie pobiera danych:**
- Sprawdź czy jesteś zalogowany do iGabinet
- Zweryfikuj uprawnienia rozszerzenia
- Sprawdź konsolę JavaScript (F12)

## 📋 **Lista kontrolna instalacji**

- [ ] Chrome w wersji 88+
- [ ] Tryb programisty włączony
- [ ] Rozszerzenie załadowane
- [ ] Ikona przypięta do paska
- [ ] URL systemu skonfigurowany
- [ ] Kod synchronizacji ustawiony
- [ ] Test połączenia udany
- [ ] Pierwszy import wykonany
- [ ] Auto-sync włączony

---
*Zobacz także: [Konfiguracja Rozszerzenia](./08-CHROME-EXTENSION-CONFIG.md)*
