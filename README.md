# KtoOstatni - System Reklamowy i Kolejkowy

## Struktura projektu po refaktoryzacji MVC

Aplikacja została zrefaktoryzowana zgodnie z wzorcem MVC i podzielona na niezależne aplikacje:

### Główny katalog `/`
- `config.php` - Główny plik konfiguracyjny dla wszystkich aplikacji
- `index.php` - Router główny kierujący żądania do odpowiednich aplikacji
- `database/` - Katalog z bazą danych SQLite
- `uploads/` - Katalog z plikami wgranymi do systemu

### `/admin/` - Panel Administracyjny
Główna aplikacja zarządzająca systemem zgodnie z wzorcem MVC:
- `controllers/` - Kontrolery aplikacji (AdminController, ClientController, QueueController, etc.)
- `models/` - <PERSON><PERSON> da<PERSON> (User, Campaign, QueueSystem, etc.)
- `views/` - <PERSON><PERSON><PERSON> (szablony HTML)
- `core/` - <PERSON><PERSON><PERSON> ap<PERSON> (Router, Database, Controller)
- `assets/` - <PERSON><PERSON>i staty<PERSON> (CSS, JS, obrazy)
- `index.php` - Punkt wejścia aplikacji admin
- `init_db.php` - Inicjalizacja bazy danych

### `/api/` - API Systemu
Niezależna aplikacja API z własną strukturą MVC:
- `controllers/` - Kontrolery API (QueueApiController, DoctorApiController, AdsApiController, etc.)
- `core/` - Rdzeń API (ApiRouter, ApiController)
- `index.php` - Punkt wejścia API

### `/display/` - Aplikacja Wyświetlacza
Aplikacja dla publicznych wyświetlaczy:
- `controllers/` - Kontrolery wyświetlacza (DisplayController)
- `index.php` - Punkt wejścia wyświetlacza

### `/pwa/` - Progressive Web App
Aplikacja PWA dla lekarzy:
- `index.html` - Główny plik PWA
- `app.js` - Logika aplikacji (zaktualizowana do nowego API)
- `manifest.json` - Manifest PWA
- `sw.js` - Service Worker

### `/chrome/` - Dodatek Chrome
Dodatek do przeglądarki Chrome:
- `manifest.json` - Manifest dodatku
- `background.js` - Skrypt tła (zaktualizowany do nowego API)
- `content.js` - Skrypt zawartości
- `popup.html/js` - Interfejs popup

### `/database/` - Bazy danych
Zawiera wszystkie pliki baz danych SQLite:
- `reklama.db` - Główna baza danych
- `reklama2.db` - Dodatkowa baza danych
- `database.sqlite3` - Baza danych systemu kolejkowego

### `/uploads/` - Pliki wgrywane
Zawiera wszystkie pliki wgrywane przez użytkowników:
- `campaigns/` - Pliki kampanii reklamowych
- `doctors/` - Zdjęcia lekarzy
- `*.csv` - Pliki CSV do importu

### `/docs/` - Dokumentacja
Zawiera wszystkie pliki dokumentacji:
- `README.md` - Główna dokumentacja
- `QUEUE_SYSTEM_UPDATE.md` - Aktualizacje systemu kolejkowego
- `CSV_IMPORT_SUMMARY.md` - Podsumowanie importu CSV
- `IMPORT_CSV_GUIDE.md` - Instrukcja importu CSV

### `/pwa/` - Progressive Web App
Zawiera pliki PWA dla lekarzy:
- `index.html` - Główna strona PWA
- `app.js` - Logika PWA
- `styles.css` - Style PWA
- `manifest.json` - Manifest PWA
- `sw.js` - Service Worker
- `icons/` - Ikony PWA

## Uruchomienie aplikacji

1. **Główna aplikacja**: Przejdź do `/admin/` w przeglądarce
2. **PWA dla lekarzy**: Przejdź do `/pwa/` w przeglądarce
3. **Dostęp publiczny**: Główny katalog `/` automatycznie przekierowuje do `/admin/`

## Konfiguracja serwera

Aplikacja wymaga serwera Apache z włączonym modułem `mod_rewrite`.

### Wymagania:
- PHP 8.0+
- Apache z mod_rewrite
- SQLite3

### Uprawnienia:
- Katalog `uploads/` musi mieć uprawnienia do zapisu
- Katalog `database/` musi mieć uprawnienia do zapisu

## Struktura URL

Po refaktoringu wszystkie URL-e aplikacji pozostają bez zmian:
- `/admin/` - Panel administratora
- `/client/` - Panel klienta
- `/login` - Logowanie
- `/register` - Rejestracja
- `/pwa/` - Aplikacja PWA dla lekarzy

## Refaktoryzacja MVC - Zmiany

### Główne zmiany po refaktoryzacji:

1. **Podział na niezależne aplikacje**:
   - Aplikacja admin (`/admin/`)
   - API systemu (`/api/`)
   - Aplikacja wyświetlacza (`/display/`)
   - PWA (`/pwa/`)
   - Dodatek Chrome (`/chrome/`)

2. **Nowy plik konfiguracyjny**:
   - `config.php` - centralna konfiguracja dla wszystkich aplikacji
   - Klasa `Config` z metodami pomocniczymi

3. **Wydzielone API**:
   - Wszystkie endpointy API przeniesione do `/api/`
   - Nowy router API z obsługą parametrów
   - Bazowa klasa `ApiController` z metodami pomocniczymi

4. **Aplikacja wyświetlacza**:
   - Niezależna aplikacja w `/display/`
   - Responsywny interfejs z automatycznym odświeżaniem
   - Integracja z API dla danych w czasie rzeczywistym

5. **Zaktualizowane aplikacje**:
   - PWA używa nowych endpointów API
   - Chrome Extension używa nowego API
   - Zachowana pełna funkcjonalność

### API Endpoints

#### Queue API
- `GET /api/queue/{clientId}` - Status kolejki
- `GET /api/queue/{clientId}/changes` - Zmiany w kolejce

#### Doctor API (PWA)
- `POST /api/doctor/login` - Logowanie lekarza
- `GET /api/doctor/appointments/{roomId}` - Wizyty w sali
- `POST /api/doctor/call-next/{roomId}` - Wywołaj następną wizytę
- `POST /api/doctor/previous/{roomId}` - Wróć do poprzedniej
- `POST /api/doctor/skip-current/{roomId}` - Pomiń aktualną
- `GET /api/doctor/stats/{roomId}` - Statystyki sali

#### Ads API
- `GET /api/ads/{clientId}` - Pobierz reklamy
- `POST /api/ads/view` - Zapisz wyświetlenie

#### Import API (Chrome Extension)
- `POST /api/import` - Import danych
- `GET /api/import/status/{syncCode}` - Status importu
- `POST /api/import/auto-map/{syncCode}` - Automatyczne mapowanie

#### Display API
- `GET /api/display/{code}` - Dane wyświetlacza
- `POST /api/display/{code}/heartbeat` - Heartbeat wyświetlacza

### Bezpieczeństwo

- Kontrola typów plików w `/uploads/`
- Walidacja danych wejściowych w API
- Obsługa błędów z logowaniem
- CSRF protection w formularzach
- Rate limiting (przygotowane do implementacji)

### Kompatybilność

Refaktoryzacja zachowuje pełną kompatybilność z istniejącymi danymi. Wszystkie ścieżki w bazie danych i konfiguracjach zostały zaktualizowane automatycznie.