<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Zarządzanie wyświetlaczami</h2>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?= $error ?>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['flash_message'] ?>
                    <?php unset($_SESSION['flash_message']); ?>
                </div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Dodaj nowy wyświetlacz</h5>
                </div>
                <div class="card-body">
                    <form action="<?= UrlHelper::url('client/displays/add') ?>" method="post">
                        <div class="mb-3">
                            <label for="display_name" class="form-label">Nazwa wyświetlacza</label>
                            <input type="text" class="form-control" id="display_name" name="display_name" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Dodaj wyświetlacz</button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5>Twoje wyświetlacze</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($displays)): ?>
                        <p>Nie masz jeszcze żadnych wyświetlaczy.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped" id="displaysTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nazwa</th>
                                        <th>Kod dostępu</th>
                                        <th>Status</th>
                                        <th>Ostatnia aktywność</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($displays as $display): ?>
                                        <tr data-display-id="<?= $display['id'] ?>">
                                            <td><?= $display['id'] ?></td>
                                            <td><?= htmlspecialchars($display['display_name']) ?></td>
                                            <td>
                                                <?php if ($display['display_code']): ?>
                                                    <code><?= $display['display_code'] ?></code>
                                                    <button class="btn btn-sm btn-outline-secondary"
                                                        onclick="navigator.clipboard.writeText('<?= $display['display_code'] ?>'); alert('Kod skopiowany do schowka');">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak kodu</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="display-status">
                                                <?php if ($display['is_online']): ?>
                                                    <span class="badge bg-success">Online</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Offline</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="display-heartbeat">
                                                <?= $display['last_heartbeat'] ? date('Y-m-d H:i:s', strtotime($display['last_heartbeat'])) : 'Nigdy' ?>
                                            </td>
                                            <td>
                                                <?php if ($display['display_code']): ?>
                                                    <a href="/wyswietlacz/<?= $display['display_code'] ?>"
                                                        class="btn btn-sm btn-primary" target="_blank">
                                                        Uruchom
                                                    </a>
                                                <?php endif; ?>
                                                <a href="<?= UrlHelper::url('client/displays/delete/' . $display['id']) ?>" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Czy na pewno chcesz usunąć ten wyświetlacz?')">
                                                    Usuń
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Funkcja do aktualizacji statusu wyświetlaczy
    function updateDisplayStatus() {
        fetch('/admin/client/displays/status')
            .then(response => response.json())
            .then(data => {
                if (data.displays) {
                    data.displays.forEach(display => {
                        const row = document.querySelector(`tr[data-display-id="${display.id}"]`);
                        if (row) {
                            const statusCell = row.querySelector('.display-status');
                            const heartbeatCell = row.querySelector('.display-heartbeat');

                            if (statusCell) {
                                statusCell.innerHTML = display.is_online ?
                                    '<span class="badge bg-success">Online</span>' :
                                    '<span class="badge bg-danger">Offline</span>';
                            }

                            if (heartbeatCell) {
                                heartbeatCell.textContent = display.last_heartbeat || 'Nigdy';
                            }
                        }
                    });
                }
            })
            .catch(error => console.error('Błąd podczas aktualizacji statusu:', error));
    }

    // Aktualizuj status co 30 sekund (30000 ms)
    setInterval(updateDisplayStatus, 30000);

    // Wywołaj aktualizację od razu po załadowaniu strony
    document.addEventListener('DOMContentLoaded', updateDisplayStatus);
</script>

<?php require_once 'views/partials/footer.php'; ?>