<?php

class DisplayController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Wyświetl publiczny interfejs wyświetlacza (oryginalny kod z ClientController)
     */
    public function show($displayCode) {
        // Kod powinien być zawsze małymi literami
        $displayCode = strtolower($displayCode);

        // Pobierz informacje o wyświetlaczu
        $stmt = $this->db->prepare("
            SELECT d.*, u.id as client_id, u.company_name
            FROM client_displays d
            JOIN users u ON d.client_id = u.id
            WHERE d.display_code = ?
        ");
        $stmt->execute([$displayCode]);
        $display = $stmt->fetch();

        if (!$display) {
            // Jeśli wyświetlacz nie istnieje, wyświetl informację o błędzie
            http_response_code(404);
            $this->showError("W<PERSON><PERSON>wietlacz o podanym kodzie nie istnieje.");
            return;
        }

        // Zaktualizuj heartbeat
        $stmt = $this->db->prepare("
            UPDATE client_displays
            SET last_heartbeat = datetime('now')
            WHERE id = ?
        ");
        $stmt->execute([$display['id']]);

        // Pobierz przypisane kampanie z uwzględnieniem ograniczeń częstotliwości
        $stmt = $this->db->prepare("
            SELECT c.*, u.company_name as advertiser_name,
                   (SELECT COUNT(*) FROM ad_views av
                    WHERE av.campaign_id = c.id
                    AND av.client_id = ?
                    AND av.timestamp >= datetime('now', '-1 hour')) as views_last_hour
            FROM campaign_assignments ca
            JOIN campaigns c ON ca.campaign_id = c.id
            JOIN users u ON c.advertiser_id = u.id
            WHERE ca.client_id = ? AND c.is_active = 1 AND ca.is_accepted = 1
            ORDER BY c.created_at
        ");
        $stmt->execute([$display["client_id"], $display["client_id"]]);
        $allCampaigns = $stmt->fetchAll();

        // Filtruj kampanie według ograniczeń częstotliwości
        $campaigns = [];
        foreach ($allCampaigns as $campaign) {
            if (Config::isDebug()) {
                error_log("Display: Sprawdzam kampanię: " . $campaign['name'] .
                    ", max_freq: " . $campaign["max_frequency_per_hour"] .
                    ", views_last_hour: " . $campaign["views_last_hour"] .
                    ", budget: " . $campaign["budget"] .
                    ", spent: " . $campaign["spent"]);
            }

            // Sprawdź ograniczenia częstotliwości
            if (
                $campaign["max_frequency_per_hour"] > 0 &&
                $campaign["views_last_hour"] >= $campaign["max_frequency_per_hour"]
            ) {
                if (Config::isDebug()) {
                    error_log("Display: Pominięto kampanię " . $campaign['name'] . " - przekroczono limit częstotliwości");
                }
                continue; // Pomiń tę kampanię - przekroczono limit
            }

            // Sprawdź czy kampania ma wystarczający budżet
            if ($campaign["budget"] <= $campaign["spent"]) {
                if (Config::isDebug()) {
                    error_log("Display: Pominięto kampanię " . $campaign['name'] . " - brak budżetu");
                }
                continue; // Pomiń tę kampanię - brak budżetu
            }

            if (Config::isDebug()) {
                error_log("Display: Dodano kampanię: " . $campaign['name']);
            }
            $campaigns[] = $campaign;
        }

        if (Config::isDebug()) {
            error_log("Display: Liczba dostępnych kampanii: " . count($campaigns));
        }

        // System kolejkowy zawsze włączony dla wyświetlaczy
        $queueEnabled = true;

        // Renderuj widok bez layoutu
        $this->renderDisplay($display, $campaigns, $queueEnabled);
    }

    /**
     * Renderuj interfejs wyświetlacza (oryginalny widok)
     */
    private function renderDisplay($display, $campaigns, $queueEnabled) {
        // Załaduj oryginalny widok z admin
        extract([
            'display' => $display,
            'campaigns' => $campaigns,
            'queueEnabled' => $queueEnabled
        ]);

        include __DIR__ . '/../views/display_public.php';
    }

    /**
     * Wyświetl błąd
     */
    private function showError($message) {
?>
        <!DOCTYPE html>
        <html lang="pl">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Błąd wyświetlacza</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>

        <body>
            <div class="container mt-5">
                <div class="alert alert-danger text-center">
                    <h4>Błąd wyświetlacza</h4>
                    <p><?php echo htmlspecialchars($message); ?></p>
                </div>
            </div>
        </body>

        </html>
<?php
    }
}
