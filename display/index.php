<?php

/**
 * Display Application Entry Point
 * Obsługuje wyświetlacze systemu kolejkowego i reklam
 */

// Załaduj konfigurację
require_once __DIR__ . '/../config.php';

// Debug
if (Config::isDebug()) {
    error_log("Display: Uruchamianie aplikacji wyświetlacza");
    error_log("Display: REQUEST_URI: " . $_SERVER['REQUEST_URI']);
}

// Pobierz kod wyświetlacza z URL
$request_uri = $_SERVER['REQUEST_URI'];

// Usuń /display/ lub /wyswietlacz/ z początku URI
if (strpos($request_uri, '/display/') === 0) {
    $displayCode = substr($request_uri, 9);
} elseif (strpos($request_uri, '/wyswietlacz/') === 0) {
    $displayCode = substr($request_uri, 13);
} else {
    // Przekieruj na stronę błędu
    http_response_code(404);
    echo "Nieprawidłowy kod wyświetlacza";
    exit;
}

// Usuń parametry GET z kodu
$displayCode = strtok($displayCode, '?');
$displayCode = trim($displayCode, '/');

if (empty($displayCode)) {
    http_response_code(400);
    echo "Brak kodu wyświetlacza";
    exit;
}

if (Config::isDebug()) {
    error_log("Display: Kod wyświetlacza: " . $displayCode);
}

// Autoloader
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/controllers/' . $className . '.php',
        __DIR__ . '/models/' . $className . '.php',
        __DIR__ . '/../admin/models/' . $className . '.php',
        __DIR__ . '/../admin/core/' . $className . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            if (defined('APP_DEBUG') && APP_DEBUG) {
                error_log("Display: Loading class $className from $path");
            }
            require_once $path;
            return;
        }
    }

    if (defined('APP_DEBUG') && APP_DEBUG) {
        error_log("Display: Class $className not found in paths: " . implode(', ', $paths));
    }
});

// Inicjalizacja bazy danych
Database::init();

// Utworzenie kontrolera wyświetlacza
$controller = new DisplayController();
$controller->show($displayCode);
