<?php
/**
 * Główny plik konfiguracyjny dla systemu KtoOstatni
 * Zawiera konfigurację dla wszystkich aplikacji: admin, api, display, pwa, chrome
 */

// Zapobieganie bezpośredniemu dostępowi
if (!defined('KTOOSTATNI_CONFIG')) {
    define('KTOOSTATNI_CONFIG', true);
}

// Podstawowe ścieżki
define('ROOT_PATH', __DIR__);
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('API_PATH', ROOT_PATH . '/api');
define('DISPLAY_PATH', ROOT_PATH . '/display');
define('PWA_PATH', ROOT_PATH . '/pwa');
define('CHROME_PATH', ROOT_PATH . '/chrome');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('DATABASE_PATH', ROOT_PATH . '/database');

// Konfiguracja bazy danych
define('DB_TYPE', 'sqlite');
define('DB_PATH', DATABASE_PATH . '/reklama.db');
define('DB_CHARSET', 'utf8');

// Konfiguracja sesji
define('SESSION_NAME', 'ktoostatni_session');
define('SESSION_LIFETIME', 3600 * 24); // 24 godziny
define('SESSION_SECURE', false); // Ustaw na true dla HTTPS
define('SESSION_HTTPONLY', true);

// Konfiguracja bezpieczeństwa
define('CSRF_TOKEN_NAME', '_token');
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minut

// Konfiguracja aplikacji
define('APP_NAME', 'KtoOstatni');
define('APP_VERSION', '2.0.0');
define('APP_DEBUG', true); // Ustaw na false w produkcji
define('APP_TIMEZONE', 'Europe/Warsaw');

// Konfiguracja URL-i
define('BASE_URL', '');
define('ADMIN_URL', BASE_URL . '/admin');
define('API_URL', BASE_URL . '/api');
define('DISPLAY_URL', BASE_URL . '/display');
define('PWA_URL', BASE_URL . '/pwa');

// Konfiguracja plików
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'webm', 'ogv']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'csv']);

// Konfiguracja systemu kolejkowego
define('QUEUE_ENABLED', true);
define('QUEUE_AUTO_REFRESH', 30); // sekundy
define('QUEUE_MAX_APPOINTMENTS_PER_DAY', 100);
define('QUEUE_DEFAULT_APPOINTMENT_DURATION', 15); // minuty

// Konfiguracja reklam
define('ADS_ENABLED', true);
define('ADS_DEFAULT_DURATION', 30); // sekundy
define('ADS_MAX_FREQUENCY_PER_HOUR', 6);
define('ADS_DEFAULT_RATE_PER_SECOND', 0.0001);

// Konfiguracja importu danych
define('IMPORT_ENABLED', true);
define('IMPORT_MAX_RECORDS_PER_BATCH', 1000);
define('IMPORT_TIMEOUT', 300); // 5 minut

// Konfiguracja Chrome Extension
define('CHROME_EXTENSION_ENABLED', true);
define('CHROME_SYNC_INTERVAL', 300); // 5 minut

// Konfiguracja PWA
define('PWA_ENABLED', true);
define('PWA_CACHE_VERSION', 'v1.0.0');
define('PWA_OFFLINE_ENABLED', true);

// Konfiguracja wyświetlaczy
define('DISPLAY_ENABLED', true);
define('DISPLAY_HEARTBEAT_INTERVAL', 60); // sekundy
define('DISPLAY_OFFLINE_THRESHOLD', 300); // 5 minut

// Konfiguracja logowania
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', ROOT_PATH . '/logs/app.log');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Konfiguracja cache
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 godzina
define('CACHE_PATH', ROOT_PATH . '/cache');

// Konfiguracja email (dla przyszłych funkcji)
define('MAIL_ENABLED', false);
define('MAIL_HOST', '');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '');
define('MAIL_FROM_NAME', APP_NAME);

/**
 * Klasa konfiguracyjna z metodami pomocniczymi
 */
class Config {
    private static $config = [];
    
    /**
     * Inicjalizacja konfiguracji
     */
    public static function init() {
        // Ustaw strefę czasową
        date_default_timezone_set(APP_TIMEZONE);
        
        // Konfiguracja sesji
        ini_set('session.name', SESSION_NAME);
        ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
        ini_set('session.cookie_lifetime', SESSION_LIFETIME);
        ini_set('session.cookie_secure', SESSION_SECURE);
        ini_set('session.cookie_httponly', SESSION_HTTPONLY);
        
        // Konfiguracja błędów
        if (APP_DEBUG) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }
        
        // Utworzenie katalogów jeśli nie istnieją
        self::createDirectories();
    }
    
    /**
     * Pobierz wartość konfiguracji
     */
    public static function get($key, $default = null) {
        return self::$config[$key] ?? $default;
    }
    
    /**
     * Ustaw wartość konfiguracji
     */
    public static function set($key, $value) {
        self::$config[$key] = $value;
    }
    
    /**
     * Sprawdź czy aplikacja jest w trybie debug
     */
    public static function isDebug() {
        return APP_DEBUG;
    }
    
    /**
     * Pobierz ścieżkę do bazy danych
     */
    public static function getDatabasePath() {
        return DB_PATH;
    }
    
    /**
     * Pobierz konfigurację bazy danych
     */
    public static function getDatabaseConfig() {
        return [
            'type' => DB_TYPE,
            'path' => DB_PATH,
            'charset' => DB_CHARSET
        ];
    }
    
    /**
     * Pobierz URL aplikacji
     */
    public static function getAppUrl($app = '') {
        switch ($app) {
            case 'admin':
                return ADMIN_URL;
            case 'api':
                return API_URL;
            case 'display':
                return DISPLAY_URL;
            case 'pwa':
                return PWA_URL;
            default:
                return BASE_URL;
        }
    }
    
    /**
     * Sprawdź czy moduł jest włączony
     */
    public static function isModuleEnabled($module) {
        switch ($module) {
            case 'queue':
                return QUEUE_ENABLED;
            case 'ads':
                return ADS_ENABLED;
            case 'import':
                return IMPORT_ENABLED;
            case 'chrome':
                return CHROME_EXTENSION_ENABLED;
            case 'pwa':
                return PWA_ENABLED;
            case 'display':
                return DISPLAY_ENABLED;
            case 'log':
                return LOG_ENABLED;
            case 'cache':
                return CACHE_ENABLED;
            case 'mail':
                return MAIL_ENABLED;
            default:
                return false;
        }
    }
    
    /**
     * Utworzenie wymaganych katalogów
     */
    private static function createDirectories() {
        $directories = [
            DATABASE_PATH,
            UPLOADS_PATH,
            UPLOADS_PATH . '/campaigns',
            UPLOADS_PATH . '/doctors',
            ROOT_PATH . '/logs',
            ROOT_PATH . '/cache'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
}

// Inicjalizacja konfiguracji
Config::init();
