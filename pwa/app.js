// Aplikacja PWA dla lekarzy
class DoctorApp {
    constructor() {
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = []; // Lista dostępnych gabinetów
        this.currentAppointment = null;
        this.waitingAppointments = [];
        this.updateInterval = null;
        this.timeInterval = null;
        this.isOnline = navigator.onLine;
        this.deferredPrompt = null; // Dla instalacji PWA
        this.apiBaseUrl = '/api'; // Nowy endpoint API

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkForStoredSession();
        this.setupOnlineOfflineHandling();
        this.initInstallPrompt();
        this.registerServiceWorker();
    }

    setupEventListeners() {
        // Formularz logowania
        const loginForm = document.getElementById('loginForm');
        const accessCodeInput = document.getElementById('accessCode');

        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Formatowanie kodu dostępu
        accessCodeInput.addEventListener('input', (e) => {
            this.formatAccessCode(e.target);
        });

        // Przyciski akcji
        document.getElementById('nextBtn').addEventListener('click', () => {
            this.callNextAppointment();
        });

        document.getElementById('previousBtn').addEventListener('click', () => {
            this.goToPreviousAppointment();
        });

        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // Nowe przyciski dla zarządzania gabinetami
        document.getElementById('changeRoomBtn').addEventListener('click', () => {
            this.showRoomSelector();
        });

        document.getElementById('roomSelector').addEventListener('change', (e) => {
            this.changeRoom(e.target.value);
        });

        // Zamykanie modalu wyboru gabinetu
        const roomModal = document.getElementById('roomModal');
        roomModal.addEventListener('click', (e) => {
            if (e.target === roomModal) {
                this.hideRoomSelector();
            }
        });

        document.getElementById('closeRoomModal').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        document.getElementById('closeRoomModalX').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        // Obsługa instalacji PWA
        document.getElementById('installBtn').addEventListener('click', () => {
            this.installApp();
        });
    }

    setupOnlineOfflineHandling() {
        // Nasłuchuj zmian stanu połączenia
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.hideOfflineMessage();
            this.loadAppointments(); // Odśwież dane po powrocie online
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineMessage();
        });

        // Sprawdź stan połączenia przy starcie
        if (!this.isOnline) {
            this.showOfflineMessage();
        }
    }

    showOfflineMessage() {
        // Usuń istniejące komunikaty
        this.hideOfflineMessage();

        const offlineAlert = document.createElement('div');
        offlineAlert.id = 'offlineAlert';
        offlineAlert.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        offlineAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        offlineAlert.innerHTML = `
            <i class="fas fa-wifi-slash me-2"></i>
            <strong>Brak połączenia z internetem!</strong> Aplikacja wymaga połączenia z serwerem.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(offlineAlert);
    }

    hideOfflineMessage() {
        const existingAlert = document.getElementById('offlineAlert');
        if (existingAlert) {
            existingAlert.remove();
        }
    }

    formatAccessCode(input) {
        let value = input.value.replace(/[^a-z0-9]/g, '').toLowerCase();

        if (value.length > 12) {
            value = value.substring(0, 12);
        }

        // Formatowanie xxxx-xxxx-xxxx
        if (value.length > 8) {
            value = value.substring(0, 4) + '-' + value.substring(4, 8) + '-' + value.substring(8);
        } else if (value.length > 4) {
            value = value.substring(0, 4) + '-' + value.substring(4);
        }

        input.value = value;
    }

    async handleLogin() {
        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        const accessCode = document.getElementById('accessCode').value.replace(/-/g, '');

        if (accessCode.length !== 12) {
            this.showError('Kod dostępu musi mieć 12 znaków');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch(`${this.apiBaseUrl}/doctor/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ access_code: accessCode })
            });

            const data = await response.json();

            if (data.success) {
                this.currentDoctor = data.doctor;
                this.currentRoom = data.default_room; // Domyślny gabinet lekarza
                this.availableRooms = data.available_rooms || []; // Lista dostępnych gabinetów

                // Zapisz sesję do północy
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    expiresAt: midnight.getTime()
                }));

                this.showMainScreen();
                this.loadAppointments();
                this.startAutoUpdate();

                // Sprawdź czy można pokazać przycisk instalacji
                if (this.deferredPrompt) {
                    this.showInstallButton();
                }
            } else {
                this.showError(data.message || 'Nieprawidłowy kod dostępu');
            }
        } catch (error) {
            console.error('Błąd logowania:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    checkForStoredSession() {
        const stored = localStorage.getItem('doctorSession');
        if (stored) {
            try {
                const session = JSON.parse(stored);
                const now = Date.now();

                // Sprawdź czy sesja nie wygasła (do północy)
                if (session.expiresAt && now < session.expiresAt) {
                    this.currentDoctor = session.doctor;
                    this.currentRoom = session.room;
                    this.availableRooms = session.availableRooms || [];
                    this.showMainScreen();
                    this.loadAppointments();
                    this.startAutoUpdate();
                } else {
                    localStorage.removeItem('doctorSession');
                }
            } catch (error) {
                localStorage.removeItem('doctorSession');
            }
        }
    }

    showMainScreen() {
        document.getElementById('loginScreen').classList.remove('active');
        document.getElementById('mainScreen').classList.add('active');

        this.updateDoctorInfo();
        this.updateRoomSelector();

        // Dodaj test ładowania danych
        console.log('Pokazuję główny ekran z danymi:', {
            doctor: this.currentDoctor,
            room: this.currentRoom,
            availableRooms: this.availableRooms
        });

        // Załaduj wizyty po pokazaniu ekranu
        setTimeout(() => {
            this.loadAppointments();
        }, 100);
    }

    updateDoctorInfo() {
        if (!this.currentDoctor || !this.currentRoom) return;

        // Informacje o lekarzu
        document.getElementById('doctorName').textContent =
            `Dr. ${this.currentDoctor.first_name} ${this.currentDoctor.last_name}`;

        document.getElementById('doctorSpecialization').textContent =
            this.currentDoctor.specialization || 'Lekarz';

        document.getElementById('roomName').textContent = this.currentRoom.name;

        // Zdjęcie lekarza
        const photoContainer = document.getElementById('doctorPhoto');
        if (this.currentDoctor.photo_url) {
            photoContainer.innerHTML = `<img src="${this.currentDoctor.photo_url}" alt="Zdjęcie lekarza">`;
        } else {
            photoContainer.innerHTML = '<i class="fas fa-user-md"></i>';
        }
    }

    updateRoomSelector() {
        const selector = document.getElementById('roomSelector');
        selector.innerHTML = '';

        // Dodaj opcję "Wybierz gabinet"
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Wybierz gabinet...';
        selector.appendChild(defaultOption);

        // Dodaj dostępne gabinety
        this.availableRooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            option.textContent = room.name;
            if (this.currentRoom && room.id === this.currentRoom.id) {
                option.selected = true;
            }
            selector.appendChild(option);
        });
    }

    showRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'flex';

        // Zaktualizuj listę gabinetów
        this.updateRoomSelector();
    }

    hideRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'none';
    }

    async changeRoom(roomId) {
        if (!roomId) return;

        const selectedRoom = this.availableRooms.find(room => room.id == roomId);
        if (!selectedRoom) return;

        this.showLoading(true);

        try {
            // Sprawdź czy gabinet jest dostępny dla tego lekarza w tym dniu
            const response = await fetch(`${this.apiBaseUrl}/doctor/check-room-availability`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    room_id: roomId,
                    date: new Date().toISOString().split('T')[0] // Dzisiejsza data
                })
            });

            const data = await response.json();

            if (data.success) {
                this.currentRoom = selectedRoom;

                // Zaktualizuj sesję
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    expiresAt: midnight.getTime()
                }));

                this.updateDoctorInfo();
                this.hideRoomSelector();

                // Odśwież wizyty dla nowego gabinetu
                await this.loadAppointments();

                this.showSuccess('Gabinety zmieniony pomyślnie');
            } else {
                this.showError(data.message || 'Nie można zmienić gabinetu');
            }
        } catch (error) {
            console.error('Błąd zmiany gabinetu:', error);
            this.showError('Błąd połączenia z serwerem');
        } finally {
            this.showLoading(false);
        }
    }

    async loadAppointments() {
        if (!this.currentRoom || !this.currentDoctor) {
            console.log('Brak danych o gabinecie lub lekarzu - pomijam ładowanie wizyt');
            return;
        }

        try {
            console.log('Ładowanie wizyt dla:', {
                roomId: this.currentRoom.id,
                doctorId: this.currentDoctor.id
            });

            const response = await fetch(`${this.apiBaseUrl}/doctor/appointments/${this.currentRoom.id}?doctor_id=${this.currentDoctor.id}`);
            const data = await response.json();

            console.log('Otrzymane dane wizyt:', data);

            if (data.success) {
                this.currentAppointment = data.current || null;
                this.waitingAppointments = data.waiting || [];

                console.log('Zaktualizowane dane:', {
                    currentAppointment: this.currentAppointment,
                    waitingCount: this.waitingAppointments.length
                });

                this.updateAppointmentDisplay();
                this.updateWaitingList();
                await this.updateStats();
            } else {
                console.error('Błąd ładowania wizyt:', data.message);
            }
        } catch (error) {
            console.error('Błąd połączenia podczas ładowania wizyt:', error);
        }
    }

    updateAppointmentDisplay() {
        const timeElement = document.getElementById('currentAppointmentTime');
        const nameElement = document.getElementById('currentPatientName');
        const nextBtn = document.getElementById('nextBtn');
        const previousBtn = document.getElementById('previousBtn');

        if (this.currentAppointment) {
            timeElement.textContent = this.currentAppointment.appointment_time;
            nameElement.textContent = this.currentAppointment.patient_name || 'Pacjent';

            // Przycisk "Następna" - aktywny tylko gdy są oczekujące wizyty
            nextBtn.disabled = this.waitingAppointments.length === 0;
            if (this.waitingAppointments.length === 0) {
                nextBtn.title = 'Brak oczekujących wizyt';
            } else {
                nextBtn.title = 'Wywołaj następną wizytę';
            }

            // Przycisk "Poprzednia" - zawsze aktywny gdy jest aktualna wizyta
            previousBtn.disabled = false;
            previousBtn.title = 'Cofnij do poprzedniej wizyty';
        } else {
            timeElement.textContent = '--:--';
            nameElement.textContent = 'Brak aktualnej wizyty';

            // Przycisk "Następna" - aktywny tylko gdy są oczekujące wizyty
            nextBtn.disabled = this.waitingAppointments.length === 0;
            if (this.waitingAppointments.length === 0) {
                nextBtn.title = 'Brak oczekujących wizyt';
            } else {
                nextBtn.title = 'Wywołaj następną wizytę';
            }

            // Przycisk "Poprzednia" - wyłączony gdy nie ma aktualnej wizyty
            previousBtn.disabled = true;
            previousBtn.title = 'Brak aktualnej wizyty do cofnięcia';
        }

        // Dodaj wizualne wskazówki o stanie przycisków
        if (nextBtn.disabled) {
            nextBtn.classList.add('btn-secondary');
            nextBtn.classList.remove('btn-success');
        } else {
            nextBtn.classList.remove('btn-secondary');
            nextBtn.classList.add('btn-success');
        }

        if (previousBtn.disabled) {
            previousBtn.classList.add('btn-secondary');
            previousBtn.classList.remove('btn-outline-secondary');
        } else {
            previousBtn.classList.remove('btn-secondary');
            previousBtn.classList.add('btn-outline-secondary');
        }
    }

    updateWaitingList() {
        const container = document.getElementById('waitingList');

        if (this.waitingAppointments.length === 0) {
            container.innerHTML = `
                <div class="no-appointments">
                    <i class="fas fa-inbox"></i>
                    <p>Brak oczekujących wizyt</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.waitingAppointments.map((appointment, index) => `
            <div class="waiting-item ${index === 0 ? 'active' : ''}" data-id="${appointment.id}">
                <div class="waiting-time">${appointment.appointment_time}</div>
                <div class="waiting-patient">${appointment.patient_name || 'Pacjent'}</div>
            </div>
        `).join('');
    }

    async updateStats() {
        const completedCount = document.getElementById('completedCount');
        const waitingCount = document.getElementById('waitingCount');

        // Liczba oczekujących (z lokalnych danych)
        waitingCount.textContent = this.waitingAppointments.length;

        // Pobierz statystyki z API
        if (this.currentRoom && this.currentDoctor) {
            try {
                console.log('Pobieranie statystyk dla:', {
                    roomId: this.currentRoom.id,
                    doctorId: this.currentDoctor.id
                });

                const response = await fetch(`${this.apiBaseUrl}/doctor/stats/${this.currentRoom.id}?doctor_id=${this.currentDoctor.id}`);
                const data = await response.json();

                console.log('Otrzymane statystyki:', data);

                if (data.success) {
                    completedCount.textContent = data.stats.completed;
                    console.log('Ustawiono zakończone wizyty:', data.stats.completed);
                } else {
                    completedCount.textContent = '0';
                    console.error('Błąd API statystyk:', data.message);
                }
            } catch (error) {
                console.error('Błąd pobierania statystyk:', error);
                completedCount.textContent = '0';
            }
        } else {
            completedCount.textContent = '0';
            console.log('Brak danych o gabinecie lub lekarzu - nie można pobrać statystyk');
        }
    }

    async callNextAppointment() {
        console.log('Wywołanie następnej wizyty...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            waitingCount: this.waitingAppointments.length
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        if (this.waitingAppointments.length === 0) {
            this.showError('Brak oczekujących wizyt do wywołania.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wysyłanie żądania do:', `/api/doctor/call-next/${this.currentRoom.id}`);
            console.log('Dane lekarza:', { doctor_id: this.currentDoctor.id });

            const response = await fetch(`${this.apiBaseUrl}/doctor/call-next/${this.currentRoom.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            console.log('Odpowiedź serwera:', response.status, response.statusText);
            const data = await response.json();
            console.log('Dane odpowiedzi:', data);

            if (data.success) {
                this.showSuccess('Następna wizyta została wywołana!');
                // Odśwież dane
                await this.loadAppointments();
                // Odśwież statystyki
                await this.updateStats();
                // Animacja przejścia
                this.animateTransition();
            } else {
                this.showError(data.message || 'Błąd podczas wywołania następnej wizyty');
            }
        } catch (error) {
            console.error('Błąd wywołania wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async goToPreviousAppointment() {
        console.log('Cofanie do poprzedniej wizyty...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            hasCurrentAppointment: !!this.currentAppointment
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        if (!this.currentAppointment) {
            this.showError('Brak aktualnej wizyty do cofnięcia.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wysyłanie żądania do:', `/api/doctor/previous/${this.currentRoom.id}`);
            console.log('Dane lekarza:', { doctor_id: this.currentDoctor.id });

            const response = await fetch(`${this.apiBaseUrl}/doctor/previous/${this.currentRoom.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            console.log('Odpowiedź serwera:', response.status, response.statusText);
            const data = await response.json();
            console.log('Dane odpowiedzi:', data);

            if (data.success) {
                this.showSuccess('Wizyta została cofnięta!');
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                this.showError(data.message || 'Błąd podczas cofania wizyty');
            }
        } catch (error) {
            console.error('Błąd cofania wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    animateTransition() {
        const appointmentDisplay = document.querySelector('.current-appointment');
        if (appointmentDisplay) {
            appointmentDisplay.classList.add('fade-in');

            setTimeout(() => {
                appointmentDisplay.classList.remove('fade-in');
            }, 500);
        }
    }

    startAutoUpdate() {
        this.updateInterval = setInterval(() => {
            if (this.isOnline) {
                this.loadAppointments();
            }
        }, 10000); // Odśwież co 10 sekund tylko gdy online
    }

    logout() {
        // Wyczyść localStorage
        localStorage.removeItem('accessCode');

        // Zatrzymaj auto-update
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        // Wyczyść dane
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = [];
        this.currentAppointment = null;
        this.waitingAppointments = [];

        // Ukryj przycisk instalacji
        this.hideInstallButton();

        // Pokaż ekran logowania
        document.getElementById('mainScreen').classList.remove('active');
        document.getElementById('loginScreen').classList.add('active');
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        const errorElement = document.getElementById('loginError');
        const messageElement = document.getElementById('errorMessage');

        messageElement.textContent = message;
        errorElement.style.display = 'block';

        // Ukryj po 5 sekundach
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    showSuccess(message) {
        // Utwórz tymczasowy komunikat sukcesu
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>Sukces!</strong> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(successAlert);

        // Usuń po 3 sekundach
        setTimeout(() => {
            if (successAlert.parentElement) {
                successAlert.remove();
            }
        }, 3000);
    }

    hideError() {
        const errorElement = document.getElementById('loginError');
        errorElement.style.display = 'none';
    }

    initInstallPrompt() {
        // Sprawdź czy przeglądarka obsługuje instalację PWA
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje PWA');
            return;
        }

        // Poproś o uprawnienia do powiadomień
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Nasłuchuj na event beforeinstallprompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: beforeinstallprompt event fired');
            // Zapobiegaj domyślnemu wyświetleniu promptu
            e.preventDefault();
            // Zapisz event do późniejszego użycia
            this.deferredPrompt = e;
            // Pokaż przycisk instalacji tylko na ekranie głównym
            if (document.getElementById('mainScreen').classList.contains('active')) {
                this.showInstallButton();
            }
        });

        // Nasłuchuj na event appinstalled
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA: Aplikacja została zainstalowana');
            this.hideInstallButton();
            this.deferredPrompt = null;

            // Pokaż powiadomienie o sukcesie
            this.showNotification('Aplikacja została zainstalowana! Możesz teraz uruchomić ją z pulpitu.', 'success');

            // Opcjonalnie: przekieruj do zainstalowanej aplikacji
            setTimeout(() => {
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    window.location.reload();
                }
            }, 2000);
        });

        // Sprawdź czy aplikacja jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana');
            this.hideInstallButton();
            return;
        }

        // Sprawdź czy przeglądarka obsługuje instalację
        if (!window.deferredPrompt) {
            console.log('PWA: Przeglądarka nie obsługuje instalacji PWA');
            this.hideInstallButton();
        } else {
            console.log('PWA: Przeglądarka obsługuje instalację PWA');
        }
    }

    showInstallButton() {
        // Sprawdź czy aplikacja nie jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana - ukrywam przycisk');
            this.hideInstallButton();
            return;
        }

        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.classList.add('show');
            console.log('PWA: Pokazuję przycisk instalacji');
        }
    }

    hideInstallButton() {
        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.classList.remove('show');
            console.log('PWA: Ukrywam przycisk instalacji');
        }
    }

    async installApp() {
        if (!this.deferredPrompt) {
            console.log('PWA: Brak promptu instalacji');

            // Sprawdź czy to Chrome/Edge
            const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
            const isEdge = /Edg/.test(navigator.userAgent);

            if (isChrome || isEdge) {
                this.showNotification('Kliknij ikonę instalacji w pasku adresu przeglądarki', 'info');
            } else {
                this.showNotification('Aplikacja nie może być zainstalowana w tej przeglądarce. Użyj Chrome lub Edge.', 'warning');
            }
            return;
        }

        try {
            // Pokaż prompt instalacji
            this.deferredPrompt.prompt();

            // Czekaj na odpowiedź użytkownika
            const { outcome } = await this.deferredPrompt.userChoice;

            console.log(`PWA: Użytkownik ${outcome === 'accepted' ? 'zaakceptował' : 'odrzucił'} instalację`);

            if (outcome === 'accepted') {
                this.showNotification('Instalacja w toku...', 'info');
            } else {
                this.showNotification('Instalacja została anulowana', 'info');
            }

            // Wyczyść prompt
            this.deferredPrompt = null;

            // Ukryj przycisk
            this.hideInstallButton();

        } catch (error) {
            console.error('PWA: Błąd podczas instalacji:', error);
            this.showNotification('Wystąpił błąd podczas instalacji', 'error');
        }
    }

    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/pwa/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);

                        // Sprawdź czy Service Worker jest aktywny
                        if (registration.active) {
                            console.log('SW is active');
                        }

                        // Nasłuchuj na aktualizacje Service Worker
                        registration.addEventListener('updatefound', () => {
                            console.log('SW update found');
                        });
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        } else {
            console.log('PWA: Service Worker nie jest obsługiwany');
        }
    }

    showNotification(message, type = 'info') {
        // Sprawdź czy przeglądarka obsługuje powiadomienia
        if (!('Notification' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje powiadomień');
            return;
        }

        // Ustaw ikonę na podstawie typu
        let icon = '/pwa/icons/icon-192x192.png';
        let title = 'KtoOstatni';

        switch (type) {
            case 'success':
                title = '✅ ' + title;
                break;
            case 'error':
                title = '❌ ' + title;
                break;
            case 'warning':
                title = '⚠️ ' + title;
                break;
            default:
                title = 'ℹ️ ' + title;
        }

        // Sprawdź uprawnienia
        if (Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: icon,
                badge: icon,
                tag: 'doctor-panel-notification',
                requireInteraction: type === 'error' || type === 'warning'
            });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(title, {
                        body: message,
                        icon: icon,
                        badge: icon,
                        tag: 'doctor-panel-notification',
                        requireInteraction: type === 'error' || type === 'warning'
                    });
                }
            });
        }
    }
}

// Inicjalizacja aplikacji
document.addEventListener('DOMContentLoaded', () => {
    new DoctorApp();
});

// Service Worker registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/pwa/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
} 