<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="client-id" content="<?= $user['id'] ?>">
    <title>Wyświetlacz Reklam - <?= htmlspecialchars($user['company_name']) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/admin/assets/css/style.css" rel="stylesheet">
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }

        .fullscreen-display {
            width: 100vw;
            height: 100vh;
            background: #000;
            position: relative;
        }

        .exit-button {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .exit-button:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .client-info {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
        }

        .status-online {
            background: rgba(28, 200, 138, 0.8) !important;
        }

        .status-offline {
            background: rgba(231, 76, 60, 0.8) !important;
        }
    </style>
</head>

<body>
    <div class="fullscreen-display">
        <!-- Przycisk powrotu -->
        <button class="exit-button" onclick="exitFullscreen()">
            <i class="fas fa-times me-2"></i>
            Wyjdź z trybu pełnoekranowego
        </button>

        <!-- Informacje o kliencie -->
        <div class="client-info">
            <strong><?= htmlspecialchars($user['company_name']) ?></strong><br>
            Monitor: #1<br>
            ID: <?= $user['id'] ?>
        </div>

        <!-- Status połączenia -->
        <div class="status-indicator status-online" id="status-indicator">
            <i class="fas fa-circle me-1"></i>
            Online
        </div>

        <!-- Kontener dla reklam -->
        <div id="ad-container" class="ad-display">
            <div class="ad-content">
                <div style="text-align: center; color: white; font-size: 2em; margin-top: 30vh;">
                    <i class="fas fa-spinner fa-spin mb-3" style="font-size: 3em; display: block;"></i>
                    <h2>Ładowanie reklam...</h2>
                    <p>Proszę czekać</p>
                </div>
            </div>
        </div>
    </div>

    <script src="/admin/assets/js/app.js"></script>
    <script>
        function exitFullscreen() {
            window.location.href = '/client';
        }

        // Automatyczne przejście do trybu pełnoekranowego
        function enterFullscreen() {
            const elem = document.documentElement;
            if (elem.requestFullscreen) {
                elem.requestFullscreen();
            } else if (elem.webkitRequestFullscreen) {
                elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) {
                elem.msRequestFullscreen();
            }
        }

        // Monitorowanie statusu połączenia
        function updateConnectionStatus() {
            const indicator = document.getElementById('status-indicator');

            fetch('/admin/client/heartbeat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'display_name=Monitor #1'
                })
                .then(response => {
                    if (response.ok) {
                        indicator.className = 'status-indicator status-online';
                        indicator.innerHTML = '<i class="fas fa-circle me-1"></i> Online';
                    } else {
                        throw new Error('Connection failed');
                    }
                })
                .catch(() => {
                    indicator.className = 'status-indicator status-offline';
                    indicator.innerHTML = '<i class="fas fa-circle me-1"></i> Offline';
                });
        }

        // Uruchom monitoring połączenia
        setInterval(updateConnectionStatus, 30000);
        updateConnectionStatus();

        // Automatyczne przejście do trybu pełnoekranowego po 3 sekundach
        setTimeout(() => {
            if (confirm('Czy chcesz przejść do trybu pełnoekranowego?')) {
                enterFullscreen();
            }
        }, 3000);

        // Obsługa klawisza ESC
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                exitFullscreen();
            }
        });

        // Zapobieganie sleep mode
        setInterval(() => {
            // Dummy request aby utrzymać aktywność
            fetch('/admin/client/heartbeat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: 'display_name=Monitor #1'
            });
        }, 60000);
    </script>
</body>

</html>