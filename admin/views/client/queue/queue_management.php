<?php require_once 'views/partials/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="mb-1">Zarządzanie kolejkami</h1>
                    <p class="text-muted mb-0">Szybkie zarządzanie wizytami wszystkich lekarzy</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?= UrlHelper::url('client/queue') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-th-large"></i> Widok karty
                    </a>
                    <a href="<?= UrlHelper::url('client/queue/doctors') ?>" class="btn btn-primary">
                        <i class="fas fa-user-md"></i> Zarząd<PERSON><PERSON> le<PERSON>
                    </a>
                </div>
            </div>

            <!-- Wyb<PERSON>r daty -->
            <div class="card mb-4">
                <div class="card-body py-3">
                    <form method="GET" action="<?= UrlHelper::url('client/queue/management') ?>" class="row align-items-center">
                        <div class="col-md-3">
                            <label for="selected_date" class="form-label mb-0">Data wizyt</label>
                            <input type="date" class="form-control" id="selected_date" name="date"
                                value="<?= htmlspecialchars($_GET['date'] ?? date('Y-m-d')) ?>"
                                min="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Pokaż wizyty
                            </button>
                            <a href="<?= UrlHelper::url('client/queue/management') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-calendar-day"></i> Dzisiaj
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Widok tabelaryczny dla szybkiego zarządzania
                            </small>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tabela kolejek -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i>
                        Kolejki lekarzy
                        <?php
                        $selectedDate = $_GET['date'] ?? date('Y-m-d');
                        $dateObj = new DateTime($selectedDate);
                        $today = new DateTime();

                        if ($dateObj->format('Y-m-d') === $today->format('Y-m-d')) {
                            echo 'dzisiaj';
                        } else {
                            echo 'z dnia ' . $dateObj->format('d.m.Y');
                        }
                        ?>
                    </h5>
                    <div>
                        <button class="btn btn-success btn-sm" onclick="callNextForAll()">
                            <i class="fas fa-play"></i> Wywołaj wszystkie
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="skipAllCurrent()">
                            <i class="fas fa-forward"></i> Pomiń wszystkie
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($doctors)): ?>
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-user-md fa-4x mb-3"></i>
                            <h4>Brak lekarzy</h4>
                            <p>Dodaj pierwszego lekarza, aby rozpocząć zarządzanie kolejkami.</p>
                            <a href="<?= UrlHelper::url('client/queue/doctors/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Dodaj lekarza
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Lekarz</th>
                                        <th>Specjalizacja</th>
                                        <th>Gabinet</th>
                                        <th>Aktualna wizyta</th>
                                        <th>Oczekujące</th>
                                        <th>Zakończone</th>
                                        <th>Status</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($doctors as $doctor): ?>
                                        <?php
                                        $doctorStats = $queueSystem->getDoctorStats($doctor['id'], $selectedDate);
                                        $currentAppointment = $queueSystem->getCurrentAppointmentForDoctor($doctor['id'], $selectedDate);
                                        $waitingAppointments = $queueSystem->getWaitingAppointmentsForDoctor($doctor['id'], 5, $selectedDate);
                                        ?>
                                        <tr class="<?= $currentAppointment ? 'table-primary' : '' ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($doctor['photo_url']): ?>
                                                        <img src="<?= htmlspecialchars($doctor['photo_url']) ?>"
                                                            alt="Zdjęcie lekarza"
                                                            style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover; margin-right: 10px;">
                                                    <?php else: ?>
                                                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: #e9ecef; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                                            <i class="fas fa-user-md"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-bold"><?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?></div>
                                                        <small class="text-muted">ID: <?= $doctor['id'] ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= htmlspecialchars($doctor['specialization'] ?: 'Brak') ?></span>
                                            </td>
                                            <td>
                                                <?php if ($doctor['default_room_name']): ?>
                                                    <i class="fas fa-door-open"></i>
                                                    <?= htmlspecialchars($doctor['default_room_name']) ?>
                                                    <?= $doctor['default_room_number'] ? ' (Gabinet ' . htmlspecialchars($doctor['default_room_number']) . ')' : '' ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak gabinetu</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($currentAppointment): ?>
                                                    <div class="alert alert-primary py-1 mb-0">
                                                        <div class="fw-bold"><?= htmlspecialchars($currentAppointment['appointment_time']) ?></div>
                                                        <small><?= htmlspecialchars($currentAppointment['patient_name'] ?: 'Pacjent') ?></small>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">Brak aktualnej wizyty</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="badge bg-warning mb-1"><?= $doctorStats['waiting_count'] ?? 0 ?> oczekujących</span>
                                                    <?php if (!empty($waitingAppointments)): ?>
                                                        <div class="small">
                                                            <?php foreach (array_slice($waitingAppointments, 0, 3) as $appointment): ?>
                                                                <div class="text-muted">
                                                                    <?= htmlspecialchars($appointment['appointment_time']) ?> -
                                                                    <?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?>
                                                                </div>
                                                            <?php endforeach; ?>
                                                            <?php if (count($waitingAppointments) > 3): ?>
                                                                <div class="text-muted">... i <?= count($waitingAppointments) - 3 ?> więcej</div>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <div class="h5 text-success mb-0"><?= $doctorStats['completed_count'] ?? 0 ?></div>
                                                    <small class="text-muted">zakończone</small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($currentAppointment): ?>
                                                    <span class="badge bg-primary">W trakcie</span>
                                                <?php elseif ($doctorStats['waiting_count'] > 0): ?>
                                                    <span class="badge bg-warning">Oczekujące</span>
                                                <?php elseif ($doctorStats['completed_count'] > 0): ?>
                                                    <span class="badge bg-success">Zakończone</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Brak wizyt</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($currentAppointment): ?>
                                                        <button class="btn btn-sm btn-success" onclick="completeAppointment(<?= $doctor['id'] ?>)" title="Zakończ wizytę">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" onclick="skipAppointment(<?= $doctor['id'] ?>)" title="Pomiń wizytę">
                                                            <i class="fas fa-forward"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-primary" onclick="callNext(<?= $doctor['id'] ?>)" title="Wywołaj następną">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <a href="<?= UrlHelper::url('client/queue/doctor-queue/' . $doctor['id']) ?>" class="btn btn-sm btn-outline-secondary" title="Panel lekarza">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="<?= UrlHelper::url('client/queue/doctors/edit/' . $doctor['id']) ?>">
                                                                    <i class="fas fa-edit"></i> Edytuj lekarza
                                                                </a></li>
                                                            <li><a class="dropdown-item" href="<?= UrlHelper::url('client/queue/doctors/generate-code/' . $doctor['id']) ?>">
                                                                    <i class="fas fa-key"></i> Generuj kod dostępu
                                                                </a></li>
                                                            <li>
                                                                <hr class="dropdown-divider">
                                                            </li>
                                                            <li><a class="dropdown-item text-danger" href="<?= UrlHelper::url('client/queue/doctors/delete/' . $doctor['id']) ?>"
                                                                    onclick="return confirm('Czy na pewno chcesz usunąć tego lekarza?')">
                                                                    <i class="fas fa-trash"></i> Usuń lekarza
                                                                </a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const selectedDate = '<?= $_GET['date'] ?? date('Y-m-d') ?>';

    function callNext(doctorId) {
        fetch(`/admin/client/queue/callNextAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas wywoływania następnej wizyty');
            });
    }

    function completeAppointment(doctorId) {
        callNext(doctorId); // Zakończenie aktualnej wizyty i wywołanie następnej
    }

    function skipAppointment(doctorId) {
        fetch(`/admin/client/queue/skipCurrentAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas pomijania wizyty');
            });
    }

    function callNextForAll() {
        if (confirm('Czy na pewno chcesz wywołać następną wizytę dla wszystkich lekarzy?')) {
            const rows = document.querySelectorAll('tbody tr');
            let completed = 0;

            rows.forEach(row => {
                const callNextBtn = row.querySelector('button[onclick*="callNext"]');
                if (callNextBtn) {
                    const onclick = callNextBtn.getAttribute('onclick');
                    const match = onclick.match(/callNext\((\d+)\)/);
                    if (match) {
                        const doctorId = match[1];
                        callNext(doctorId);
                        completed++;
                    }
                }
            });

            if (completed === 0) {
                alert('Nie znaleziono lekarzy do wywołania');
            }
        }
    }

    function skipAllCurrent() {
        if (confirm('Czy na pewno chcesz pominąć aktualne wizyty dla wszystkich lekarzy?')) {
            const rows = document.querySelectorAll('tbody tr');
            let completed = 0;

            rows.forEach(row => {
                const skipBtn = row.querySelector('button[onclick*="skipAppointment"]');
                if (skipBtn) {
                    const onclick = skipBtn.getAttribute('onclick');
                    const match = onclick.match(/skipAppointment\((\d+)\)/);
                    if (match) {
                        const doctorId = match[1];
                        skipAppointment(doctorId);
                        completed++;
                    }
                }
            });

            if (completed === 0) {
                alert('Nie znaleziono lekarzy z aktualnymi wizytami');
            }
        }
    }

    // Auto-refresh co 30 sekund
    setInterval(() => {
        if (!document.hidden) {
            location.reload();
        }
    }, 30000);
</script>

<?php require_once 'views/partials/footer.php'; ?>