<?php require_once 'views/partials/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Panel lekarza - <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?></h1>
                <div>
                    <a href="<?= UrlHelper::url('client/queue/doctors') ?>" class="btn btn-secondary">Powrót do listy lekarzy</a>
                </div>
            </div>

            <!-- Wybór daty -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Wyb<PERSON>r daty</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= UrlHelper::url('client/queue/doctor-queue/' . $doctor['id']) ?>" class="row align-items-end">
                        <div class="col-md-4">
                            <label for="selected_date" class="form-label">Data wizyt</label>
                            <div class="d-flex gap-2">
                                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime($selectedDate . ' -1 day')) ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <input type="date" class="form-control" id="selected_date" name="date"
                                    value="<?= htmlspecialchars($selectedDate) ?>">
                                <button type="submit" name="date" value="<?= date('Y-m-d', strtotime($selectedDate . ' +1 day')) ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Pokaż wizyty
                            </button>
                            <a href="<?= UrlHelper::url('client/queue/doctor-queue/' . $doctor['id']) ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-calendar-day"></i> Dzisiaj
                            </a>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                Wybierz datę, aby przeglądać wizyty z różnych dni
                            </small>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Informacje o lekarzu -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex align-items-center">
                    <?php if ($doctor['photo_url']): ?>
                        <img src="<?= htmlspecialchars($doctor['photo_url']) ?>"
                            alt="Zdjęcie lekarza"
                            style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover; margin-right: 15px;">
                    <?php else: ?>
                        <div style="width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-user-md"></i>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h5 class="mb-0"><?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?></h5>
                        <?php if ($doctor['specialization']): ?>
                            <small class="text-white-50"><?= htmlspecialchars($doctor['specialization']) ?></small>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-0">
                        <strong>Domyślny gabinet:</strong> <?= htmlspecialchars($doctor['default_room_name'] ?: 'Brak przypisanego gabinetu') ?>
                        <?= $doctor['default_room_number'] ? ' (Gabinet ' . htmlspecialchars($doctor['default_room_number']) . ')' : '' ?>
                    </p>
                </div>
            </div>

            <!-- Statystyki -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?= $doctorStats['waiting_count'] ?? 0 ?></h5>
                            <p class="card-text">Oczekujących</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?= $doctorStats['completed_count'] ?? 0 ?></h5>
                            <p class="card-text">Zakończonych</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?= $doctorStats['current_count'] ?? 0 ?></h5>
                            <p class="card-text">Aktualnych</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger"><?= $doctorStats['cancelled_count'] ?? 0 ?></h5>
                            <p class="card-text">Anulowanych</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Aktualna wizyta -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Aktualna wizyta</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($currentAppointment): ?>
                                <div class="display-4 text-primary mb-3">
                                    <?= htmlspecialchars($currentAppointment['appointment_time']) ?>
                                </div>
                                <h5><?= htmlspecialchars($currentAppointment['patient_name'] ?: 'Pacjent') ?></h5>
                                <?php if ($currentAppointment['room_name']): ?>
                                    <p class="text-muted">Gabinet: <?= htmlspecialchars($currentAppointment['room_name']) ?></p>
                                <?php endif; ?>
                                <div class="mt-3">
                                    <button class="btn btn-success btn-lg me-2" onclick="completeCurrent()">
                                        <i class="fas fa-check"></i> Zakończ wizytę
                                    </button>
                                    <button class="btn btn-warning btn-lg me-2" onclick="skipCurrent()">
                                        <i class="fas fa-forward"></i> Pomiń
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="display-4 text-muted mb-3">--:--</div>
                                <h5>Brak aktualnej wizyty</h5>
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-lg" onclick="callNext()">
                                        <i class="fas fa-play"></i> Wywołaj następną
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Oczekujące wizyty -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Oczekujące wizyty</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($waitingAppointments)): ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-clock fa-3x mb-3"></i>
                                    <p>Brak oczekujących wizyt</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($waitingAppointments as $appointment): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?= htmlspecialchars($appointment['appointment_time']) ?></h6>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?>
                                                    <?php if ($appointment['room_name']): ?>
                                                        • Gabinet: <?= htmlspecialchars($appointment['room_name']) ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="d-flex align-items-center gap-1">
                                                <span class="badge bg-primary rounded-pill">
                                                    <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                                </span>
                                                <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $appointment['id']) ?>" class="btn btn-outline-primary btn-sm ms-1" title="Edytuj">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>" class="btn btn-outline-danger btn-sm ms-1" title="Usuń" onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lista wszystkich wizyt -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Wszystkie wizyty
                        <?php
                        $dateObj = new DateTime($selectedDate);
                        $today = new DateTime();

                        if ($dateObj->format('Y-m-d') === $today->format('Y-m-d')) {
                            echo 'dzisiaj';
                        } else {
                            echo 'z dnia ' . $dateObj->format('d.m.Y');
                        }
                        ?>
                    </h5>
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
                        <i class="fas fa-plus"></i> Dodaj wizytę
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($allAppointments)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar fa-3x mb-3"></i>
                            <p>
                                Brak wizyt
                                <?php
                                $dateObj = new DateTime($selectedDate);
                                $today = new DateTime();

                                if ($dateObj->format('Y-m-d') === $today->format('Y-m-d')) {
                                    echo 'na dzisiaj';
                                } else {
                                    echo 'na dzień ' . $dateObj->format('d.m.Y');
                                }
                                ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Godzina</th>
                                        <th>Pacjent</th>
                                        <th>Gabinet</th>
                                        <th>Status</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allAppointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($appointment['appointment_time']) ?></strong>
                                            </td>
                                            <td><?= htmlspecialchars($appointment['patient_name'] ?: 'Pacjent') ?></td>
                                            <td>
                                                <?= htmlspecialchars($appointment['room_name'] ?: 'Brak gabinetu') ?>
                                                <?= $appointment['room_number'] ? ' (Gabinet ' . htmlspecialchars($appointment['room_number']) . ')' : '' ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($appointment['status']) {
                                                    case 'waiting':
                                                        $statusClass = 'bg-warning';
                                                        $statusText = 'Oczekująca';
                                                        break;
                                                    case 'current':
                                                        $statusClass = 'bg-primary';
                                                        $statusText = 'W trakcie';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'Zakończona';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'Anulowana';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= UrlHelper::url('client/queue/appointments/edit/' . $appointment['id']) ?>"
                                                        class="btn btn-outline-primary" title="Edytuj">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= UrlHelper::url('client/queue/appointments/delete/' . $appointment['id']) ?>"
                                                        class="btn btn-outline-danger" title="Usuń"
                                                        onclick="return confirm('Czy na pewno chcesz usunąć tę wizytę?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal dodawania wizyty -->
<div class="modal fade" id="addAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dodaj nową wizytę</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addAppointmentForm">
                <div class="modal-body">
                    <input type="hidden" name="doctor_id" value="<?= $doctor['id'] ?>">
                    <input type="hidden" name="appointment_date" value="<?= $selectedDate ?>">

                    <div class="mb-3">
                        <label for="appointment_time" class="form-label">Godzina wizyty</label>
                        <input type="time" class="form-control" id="appointment_time" name="appointment_time" required>
                    </div>

                    <div class="mb-3">
                        <label for="patient_name" class="form-label">Imię i nazwisko pacjenta</label>
                        <input type="text" class="form-control" id="patient_name" name="patient_name" placeholder="Opcjonalne">
                    </div>

                    <div class="mb-3">
                        <label for="room_id" class="form-label">Gabinet</label>
                        <select class="form-control" id="room_id" name="room_id">
                            <option value="">Użyj domyślnego gabinetu</option>
                            <?php
                            $queueSystem = new QueueSystem();
                            $rooms = $queueSystem->getRooms($user['id']);
                            foreach ($rooms as $room):
                            ?>
                                <option value="<?= $room['id'] ?>">
                                    <?= htmlspecialchars($room['name']) ?>
                                    <?= $room['room_number'] ? ' (Gabinet ' . htmlspecialchars($room['room_number']) . ')' : '' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-primary">Dodaj wizytę</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    const doctorId = <?= $doctor['id'] ?>;
    const selectedDate = '<?= $selectedDate ?>';

    function callNext() {
        fetch(`/admin/client/queue/callNextAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => {
                if (response.redirected) {
                    // Jeśli zostaliśmy przekierowani (np. na login), przekieruj użytkownika
                    window.location.href = response.url;
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    location.reload();
                } else if (data) {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas wywoływania następnej wizyty');
            });
    }

    function completeCurrent() {
        callNext(); // Zakończenie aktualnej wizyty i wywołanie następnej
    }

    function skipCurrent() {
        fetch(`/admin/client/queue/skipCurrentAppointmentForDoctor/${doctorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `date=${selectedDate}`
            })
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    location.reload();
                } else if (data) {
                    alert(data.message || 'Wystąpił błąd');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas pomijania wizyty');
            });
    }

    // Obsługa formularza dodawania wizyty
    document.getElementById('addAppointmentForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = new URLSearchParams(formData);

        fetch('/admin/client/queue/addAppointmentForDoctor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: data
            })
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                    return;
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    location.reload();
                } else if (data) {
                    alert(data.error || 'Wystąpił błąd podczas dodawania wizyty');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Wystąpił błąd podczas dodawania wizyty');
            });
    });

    // Otwórz modal jeśli w URL jest #addAppointmentModal
    if (window.location.hash === '#addAppointmentModal') {
        document.addEventListener('DOMContentLoaded', function() {
            var modal = new bootstrap.Modal(document.getElementById('addAppointmentModal'));
            modal.show();
        });
    }
</script>

<?php require_once 'views/partials/footer.php'; ?>