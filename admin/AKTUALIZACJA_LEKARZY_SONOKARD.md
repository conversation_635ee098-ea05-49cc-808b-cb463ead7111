# 🔄 Aktualizacja Listy Lekarzy Sonokard

## ✅ Zakończono Pomyślnie!

Lista lekarzy Sonokard została zaktualizowana z pliku `uploads/sonokard_zespol_source.html` wraz ze zdjęciami.

## 📊 Podsumowanie Aktualizacji:

### 🔍 **Parsowanie HTML:**
- ✅ **Znaleziono:** 16 lekarzy w pliku HTML
- ✅ **Zdjęcia:** 8 lekarzy ma zdjęcia z sonokard.pl
- ✅ **Filtrowanie:** Usunięto adresy, telefony i inne niepotrzebne dane

### 📸 **Zdjęcia Lekarzy:**
Zaktualizowano zdjęcia dla następujących lekarzy:

| Lekarz | Zdjęcie |
|--------|---------|
| lek. Oliwia Kopera | ✅ https://sonokard.pl/fileadmin/_processed_/7/7/csm_162www_2090344a6a.jpg |
| mgr <PERSON><PERSON>ws<PERSON> | ✅ https://sonokard.pl/fileadmin/_processed_/e/8/csm_123www_db7b429856.jpg |
| dr n. med. Barbara Stachowska | ✅ https://sonokard.pl/fileadmin/_processed_/1/8/csm_11038_34d47de9f6.jpg |
| lek. Yuliia Baraniak | ✅ https://sonokard.pl/fileadmin/_processed_/8/e/csm_Yuliia_Baraniak-zaakceptowane_01_e32db26d86.jpg |

### 💾 **Eksport do init_db.php:**
- ✅ **Wyeksportowano:** 41 lekarzy do pliku `admin/init_db.php`
- ✅ **Zawiera:** Imiona, nazwiska, specjalizacje, zdjęcia i kody dostępu
- ✅ **Automatyczne:** Przy każdej inicjalizacji bazy danych lekarze będą dodani

## 🛠️ Narzędzia i Skrypty:

### 1. **Synchronizacja Lekarzy:**
```bash
php admin/sync_sonokard_doctors.php
```
- Parsuje plik `uploads/sonokard_zespol_source.html`
- Dodaje nowych lekarzy do bazy danych
- Aktualizuje zdjęcia istniejących lekarzy

### 2. **Eksport do init_db.php:**
```bash
php admin/export_doctors_to_init.php
```
- Eksportuje aktualną listę lekarzy z bazy danych
- Aktualizuje plik `admin/init_db.php`
- Zapewnia, że przy reinicjalizacji bazy dane lekarzy są zachowane

### 3. **Test Parsowania:**
```bash
php admin/test_regex.php
```
- Testuje wzorce regex na pliku HTML
- Debuguje parsowanie nazw, specjalizacji i zdjęć

## 📋 Struktura Danych Lekarzy:

### Baza Danych (tabela `queue_doctors`):
```sql
CREATE TABLE queue_doctors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id INTEGER,
    first_name TEXT,
    last_name TEXT,
    specialization TEXT,
    photo_url TEXT,
    access_code TEXT,
    active INTEGER DEFAULT 1,
    created_at DATETIME
);
```

### Format w init_db.php:
```php
$sonokardDoctors = [
    [
        'first_name' => 'dr n. med. Małgorzata',
        'last_name' => 'Olesiak-Andryszczak',
        'specialization' => 'Specjalista ginekologii i położnictwa, Specjalista perinatologii',
        'photo_url' => 'https://sonokard.pl/fileadmin/_processed_/2/0/csm_2291_01_698d3e2449.jpg',
        'access_code' => 'DOC001'
    ],
    // ... więcej lekarzy
];
```

## 🔄 Proces Aktualizacji:

### Krok 1: Pobierz Nowy HTML
1. Idź na stronę: https://sonokard.pl/zespol
2. Zapisz źródło strony jako `uploads/sonokard_zespol_source.html`
3. Lub skrypt automatycznie pobierze nową wersję

### Krok 2: Uruchom Synchronizację
```bash
php admin/sync_sonokard_doctors.php
```

### Krok 3: Eksportuj do init_db.php
```bash
php admin/export_doctors_to_init.php
```

## 📸 Obsługa Zdjęć:

### Źródła Zdjęć:
- **Sonokard.pl:** Automatycznie pobierane z HTML
- **Lokalne:** Przechowywane w `/uploads/doctors/`
- **Priorytet:** Lokalne zdjęcia mają pierwszeństwo

### Format URL Zdjęć:
- **Sonokard:** `https://sonokard.pl/fileadmin/_processed_/...`
- **Lokalne:** `/uploads/doctors/doctor1.webp`

## 🎯 Rezultaty:

### Aktualna Lista Lekarzy (41 lekarzy):
- **Ginekolodzy:** 16 lekarzy
- **Kardiolodzy:** 3 lekarzy  
- **Dermatolodzy:** 2 lekarzy
- **Endokrynolodzy:** 2 lekarzy
- **Pediatrzy:** 3 lekarzy
- **Chirurdzy:** 3 lekarzy
- **Położne:** 3 położne
- **Inni specjaliści:** 9 lekarzy

### Zdjęcia:
- **Ze zdjęciami:** 15 lekarzy
- **Sonokard.pl:** 4 nowe zdjęcia
- **Lokalne:** 11 zdjęć

## 🔧 Rozwiązywanie Problemów:

### Problem: Parser nie znajduje lekarzy
**Rozwiązanie:**
1. Sprawdź plik `uploads/sonokard_zespol_source.html`
2. Uruchom `php admin/test_regex.php` do debugowania
3. Zaktualizuj wzorce regex w `sync_sonokard_doctors.php`

### Problem: Brak zdjęć
**Rozwiązanie:**
1. Sprawdź czy URL zdjęć w HTML są poprawne
2. Sprawdź metodę `extractPhotoUrl()` w skrypcie
3. Zdjęcia są pobierane tylko jeśli są dostępne w HTML

### Problem: Duplikaty lekarzy
**Rozwiązanie:**
- Skrypt automatycznie sprawdza duplikaty po imieniu i nazwisku
- Istniejący lekarze są pomijani, aktualizowane są tylko zdjęcia

## 📅 Harmonogram Aktualizacji:

### Zalecane:
- **Miesięcznie:** Sprawdź zmiany na sonokard.pl/zespol
- **Po zmianach:** Uruchom synchronizację gdy dodani są nowi lekarze
- **Przed wdrożeniem:** Zawsze eksportuj do init_db.php

---

**🎉 Lista lekarzy Sonokard została pomyślnie zaktualizowana ze zdjęciami i wyeksportowana do init_db.php!**
