<?php

class Doctor<PERSON><PERSON><PERSON>ontroller extends Controller {
    private $queueSystem;

    public function __construct() {
        parent::__construct();
        $this->queueSystem = new QueueSystem();
    }

    // Logowanie lekarza przez kod dostępu
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $accessCode = $input['access_code'] ?? '';

        if (empty($accessCode) || strlen($accessCode) !== 12) {
            $this->json(['success' => false, 'message' => 'Nieprawidłowy kod dostępu']);
            return;
        }

        // Znajdź lekarza po kodzie dostępu
        $stmt = $this->db->prepare("
            SELECT d.*, r.id as default_room_id, r.name as default_room_name, r.room_number, c.id as client_id
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            LEFT JOIN users c ON r.client_id = c.id
            WHERE d.access_code = ? AND d.active = 1
        ");
        $stmt->execute([$accessCode]);
        $doctor = $stmt->fetch();

        if (!$doctor) {
            $this->json(['success' => false, 'message' => 'Nieprawidłowy kod dostępu lub lekarz nieaktywny']);
            return;
        }

        // Przygotuj dane lekarza
        $doctorData = [
            'id' => $doctor['id'],
            'first_name' => $doctor['first_name'],
            'last_name' => $doctor['last_name'],
            'specialization' => $doctor['specialization'],
            'photo_url' => $doctor['photo_url'],
            'default_room_id' => $doctor['default_room_id']
        ];

        // Pobierz domyślny gabinet (jeśli istnieje)
        $defaultRoom = null;
        if ($doctor['default_room_id']) {
            $defaultRoom = [
                'id' => $doctor['default_room_id'],
                'name' => $doctor['default_room_name'],
                'room_number' => $doctor['room_number'],
                'client_id' => $doctor['client_id']
            ];
        }

        // Pobierz listę dostępnych gabinetów dla tego lekarza
        $availableRooms = $this->getAvailableRooms($doctor['id']);

        $this->json([
            'success' => true,
            'doctor' => $doctorData,
            'default_room' => $defaultRoom,
            'available_rooms' => $availableRooms
        ]);
    }

    // Pobieranie dostępnych gabinetów dla lekarza
    private function getAvailableRooms($doctorId) {
        $stmt = $this->db->prepare("
            SELECT DISTINCT r.id, r.name, r.room_number, c.id as client_id, c.company_name as client_name
            FROM queue_rooms r
            JOIN users c ON r.client_id = c.id
            WHERE r.active = 1 AND c.is_active = 1
            ORDER BY r.name
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    // Sprawdzanie dostępności gabinetu dla lekarza
    public function checkRoomAvailability() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $doctorId = $input['doctor_id'] ?? null;
        $roomId = $input['room_id'] ?? null;
        $date = $input['date'] ?? date('Y-m-d');

        if (!$doctorId || !$roomId) {
            $this->json(['success' => false, 'message' => 'Brak wymaganych parametrów']);
            return;
        }

        // Sprawdź czy lekarz istnieje i jest aktywny
        $stmt = $this->db->prepare("SELECT id FROM queue_doctors WHERE id = ? AND active = 1");
        $stmt->execute([$doctorId]);
        if (!$stmt->fetch()) {
            $this->json(['success' => false, 'message' => 'Lekarz nie istnieje lub nieaktywny']);
            return;
        }

        // Sprawdź czy gabinet istnieje i jest aktywny
        $stmt = $this->db->prepare("SELECT id FROM queue_rooms WHERE id = ? AND active = 1");
        $stmt->execute([$roomId]);
        if (!$stmt->fetch()) {
            $this->json(['success' => false, 'message' => 'Gabinet nie istnieje lub nieaktywny']);
            return;
        }

        // Sprawdź czy gabinet nie jest już zajęty przez innego lekarza w tym dniu
        $stmt = $this->db->prepare("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE room_id = ? AND DATE(appointment_date) = ? AND doctor_id != ? AND status IN ('waiting', 'current')
        ");
        $stmt->execute([$roomId, $date, $doctorId]);
        $conflicts = $stmt->fetchColumn();

        if ($conflicts > 0) {
            $this->json(['success' => false, 'message' => 'Gabinet jest już zajęty przez innego lekarza w tym dniu']);
            return;
        }

        $this->json(['success' => true, 'message' => 'Gabinet dostępny']);
    }

    // Pobieranie wizyt dla lekarza w konkretnym gabinecie
    public function appointments($roomId) {
        if (!$this->validateRoomAccess($roomId)) {
            $this->json(['success' => false, 'message' => 'Brak dostępu do sali']);
            return;
        }

        // Pobierz ID lekarza z parametrów
        $doctorId = $_GET['doctor_id'] ?? null;
        if (!$doctorId) {
            $this->json(['success' => false, 'message' => 'Brak ID lekarza']);
            return;
        }

        // Pobierz aktualną wizytę dla konkretnego lekarza
        $currentAppointment = $this->getCurrentAppointmentForDoctor($roomId, $doctorId);

        // Pobierz listę oczekujących wizyt dla konkretnego lekarza
        $waitingAppointments = $this->getWaitingAppointmentsForDoctor($roomId, $doctorId, 10);

        $this->json([
            'success' => true,
            'current' => $currentAppointment,
            'waiting' => $waitingAppointments
        ]);
    }

    // Pobieranie aktualnej wizyty dla konkretnego lekarza
    private function getCurrentAppointmentForDoctor($roomId, $doctorId) {
        $stmt = $this->db->prepare("
            SELECT a.*, a.patient_name, '' as patient_phone
            FROM queue_appointments a
            WHERE a.room_id = ? AND a.doctor_id = ? AND a.status = 'current'
            ORDER BY a.appointment_time ASC
            LIMIT 1
        ");
        $stmt->execute([$roomId, $doctorId]);
        return $stmt->fetch();
    }

    // Pobieranie oczekujących wizyt dla konkretnego lekarza
    private function getWaitingAppointmentsForDoctor($roomId, $doctorId, $limit = 10) {
        $stmt = $this->db->prepare("
            SELECT a.*, a.patient_name, '' as patient_phone
            FROM queue_appointments a
            WHERE a.room_id = ? AND a.doctor_id = ? AND a.status = 'waiting'
            ORDER BY a.appointment_time ASC
            LIMIT ?
        ");
        $stmt->execute([$roomId, $doctorId, $limit]);
        return $stmt->fetchAll();
    }

    // Wywołanie następnej wizyty dla konkretnego lekarza
    public function callNext($roomId) {
        if (!$this->validateRoomAccess($roomId)) {
            $this->json(['success' => false, 'message' => 'Brak dostępu do sali']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $doctorId = $input['doctor_id'] ?? null;

        if (!$doctorId) {
            $this->json(['success' => false, 'message' => 'Brak ID lekarza']);
            return;
        }

        $nextAppointment = $this->callNextAppointmentForDoctor($roomId, $doctorId);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment' => $nextAppointment
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak kolejnych wizyt w kolejce']);
        }
    }

    // Wywołanie następnej wizyty dla konkretnego lekarza
    private function callNextAppointmentForDoctor($roomId, $doctorId) {
        // Zakończ aktualną wizytę (jeśli istnieje)
        $stmt = $this->db->prepare("
            UPDATE queue_appointments 
            SET status = 'completed', completed_at = datetime('now') 
            WHERE room_id = ? AND doctor_id = ? AND status = 'current'
        ");
        $stmt->execute([$roomId, $doctorId]);

        // Pobierz następną wizytę z kolejki
        $stmt = $this->db->prepare("
            SELECT a.*, a.patient_name, '' as patient_phone
            FROM queue_appointments a
            WHERE a.room_id = ? AND a.doctor_id = ? AND a.status = 'waiting'
            ORDER BY a.appointment_time ASC
            LIMIT 1
        ");
        $stmt->execute([$roomId, $doctorId]);
        $nextAppointment = $stmt->fetch();

        if ($nextAppointment) {
            // Zmień status na 'current'
            $stmt = $this->db->prepare("
                UPDATE queue_appointments 
                SET status = 'current', started_at = datetime('now') 
                WHERE id = ?
            ");
            $stmt->execute([$nextAppointment['id']]);

            $nextAppointment['status'] = 'current';
            $nextAppointment['started_at'] = date('Y-m-d H:i:s');
        }

        return $nextAppointment;
    }

    // Cofanie do poprzedniej wizyty dla konkretnego lekarza
    public function previous($roomId) {
        if (!$this->validateRoomAccess($roomId)) {
            $this->json(['success' => false, 'message' => 'Brak dostępu do sali']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $doctorId = $input['doctor_id'] ?? null;

        if (!$doctorId) {
            $this->json(['success' => false, 'message' => 'Brak ID lekarza']);
            return;
        }

        $previousAppointment = $this->callPreviousAppointmentForDoctor($roomId, $doctorId);

        if ($previousAppointment === false) {
            $this->json(['success' => false, 'message' => 'Wystąpił błąd podczas cofania wizyty.']);
        } elseif ($previousAppointment === null) {
            $this->json(['success' => false, 'message' => 'Brak poprzednich wizyt w historii.']);
        } else {
            $this->json([
                'success' => true,
                'current' => $previousAppointment,
                'waiting' => $this->getWaitingAppointmentsForDoctor($roomId, $doctorId, 10)
            ]);
        }
    }

    // Cofanie do poprzedniej wizyty dla konkretnego lekarza
    private function callPreviousAppointmentForDoctor($roomId, $doctorId) {
        // Pobierz aktualną wizytę
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments 
            WHERE room_id = ? AND doctor_id = ? AND status = 'current'
        ");
        $stmt->execute([$roomId, $doctorId]);
        $currentAppointment = $stmt->fetch();

        if (!$currentAppointment) {
            return null;
        }

        // Zmień status aktualnej wizyty na 'waiting'
        $stmt = $this->db->prepare("
            UPDATE queue_appointments 
            SET status = 'waiting', started_at = NULL 
            WHERE id = ?
        ");
        $stmt->execute([$currentAppointment['id']]);

        // Pobierz ostatnią zakończoną wizytę
        $stmt = $this->db->prepare("
            SELECT a.*, a.patient_name, '' as patient_phone
            FROM queue_appointments a
            WHERE a.room_id = ? AND a.doctor_id = ? AND a.status = 'completed'
            ORDER BY a.completed_at DESC
            LIMIT 1
        ");
        $stmt->execute([$roomId, $doctorId]);
        $previousAppointment = $stmt->fetch();

        if ($previousAppointment) {
            // Przywróć ostatnią zakończoną wizytę
            $stmt = $this->db->prepare("
                UPDATE queue_appointments 
                SET status = 'current', completed_at = NULL, started_at = datetime('now') 
                WHERE id = ?
            ");
            $stmt->execute([$previousAppointment['id']]);

            $previousAppointment['status'] = 'current';
            $previousAppointment['started_at'] = date('Y-m-d H:i:s');
            $previousAppointment['completed_at'] = null;
        }

        return $previousAppointment;
    }

    // Pomijanie aktualnej wizyty
    public function skipCurrent($roomId) {
        if (!$this->validateRoomAccess($roomId)) {
            $this->json(['success' => false, 'message' => 'Brak dostępu do sali']);
            return;
        }

        $nextAppointment = $this->queueSystem->skipCurrentAppointment($roomId);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment' => $nextAppointment
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak aktualnej wizyty lub kolejnych wizyt']);
        }
    }

    // Pobieranie statystyk dla lekarza
    public function stats($roomId) {
        if (!$this->validateRoomAccess($roomId)) {
            $this->json(['success' => false, 'message' => 'Brak dostępu do sali']);
            return;
        }

        $doctorId = $_GET['doctor_id'] ?? null;
        if (!$doctorId) {
            $this->json(['success' => false, 'message' => 'Brak ID lekarza']);
            return;
        }

        $today = date('Y-m-d');

        // Liczba zakończonych wizyt dzisiaj dla konkretnego lekarza
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as completed
            FROM queue_appointments
            WHERE room_id = ? AND doctor_id = ? AND status = 'completed' 
            AND DATE(completed_at) = ?
        ");
        $stmt->execute([$roomId, $doctorId, $today]);
        $completed = $stmt->fetchColumn();

        // Liczba oczekujących wizyt dla konkretnego lekarza
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as waiting
            FROM queue_appointments
            WHERE room_id = ? AND doctor_id = ? AND status = 'waiting'
        ");
        $stmt->execute([$roomId, $doctorId]);
        $waiting = $stmt->fetchColumn();

        $this->json([
            'success' => true,
            'stats' => [
                'completed' => $completed,
                'waiting' => $waiting
            ]
        ]);
    }

    // Walidacja dostępu do sali (przez kod dostępu)
    private function validateRoomAccess($roomId) {
        // W prawdziwej aplikacji tutaj byłaby walidacja tokena lub sesji
        // Na razie sprawdzamy tylko czy sala istnieje i jest aktywna
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND active = 1
        ");
        $stmt->execute([$roomId]);
        return $stmt->fetchColumn() > 0;
    }

    // Generowanie kodu dostępu dla lekarza
    public function generateAccessCode($doctorId) {
        // Sprawdź czy lekarz istnieje
        $stmt = $this->db->prepare("SELECT id FROM queue_doctors WHERE id = ?");
        $stmt->execute([$doctorId]);

        if (!$stmt->fetch()) {
            return false;
        }

        // Generuj kod dostępu (12 znaków, tylko małe litery i cyfry)
        $accessCode = $this->generateRandomCode();

        // Sprawdź czy kod już istnieje
        $stmt = $this->db->prepare("SELECT id FROM queue_doctors WHERE access_code = ?");
        $stmt->execute([$accessCode]);

        while ($stmt->fetch()) {
            $accessCode = $this->generateRandomCode();
            $stmt->execute([$accessCode]);
        }

        // Zapisz kod dostępu
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET access_code = ? 
            WHERE id = ?
        ");

        return $stmt->execute([$accessCode, $doctorId]);
    }

    // Generowanie losowego kodu dostępu
    private function generateRandomCode() {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';

        for ($i = 0; $i < 12; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $code;
    }
}
