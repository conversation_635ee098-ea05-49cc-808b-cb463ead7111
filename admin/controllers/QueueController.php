<?php

class QueueController extends Controller {
    private $queueSystem;

    public function __construct() {
        parent::__construct();
        $this->queueSystem = new QueueSystem();
    }

    // Strona główna zarządzania systemem kolejkowym
    public function index() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $isEnabled = $this->queueSystem->isEnabled($user['id']);
        $rooms = $this->queueSystem->getRooms($user['id']);
        $doctors = $this->queueSystem->getDoctors($user['id']);

        // Pobierz statystyki dla wszystkich sal
        $stats = [];
        if ($isEnabled) {
            $selectedDate = $_GET['date'] ?? date('Y-m-d');
            // Sprawdź czy data jest poprawna
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $selectedDate)) {
                $selectedDate = date('Y-m-d');
            }
            $stats = $this->queueSystem->getAppointmentStats($user['id'], $selectedDate);
        }

        $this->view('client/queue/index', [
            'user' => $user,
            'isEnabled' => $isEnabled,
            'rooms' => $rooms,
            'doctors' => $doctors,
            'stats' => $stats,
            'queueSystem' => $this->queueSystem,
            'selectedDate' => $selectedDate
        ]);
    }

    // Włączanie lub wyłączanie systemu kolejkowego
    public function toggleSystem() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue');
            return;
        }

        $user = $this->getCurrentUser();
        $isEnabled = isset($_POST['is_enabled']) && $_POST['is_enabled'] == 1;

        if ($this->queueSystem->toggleSystem($user['id'], $isEnabled)) {
            $_SESSION['flash_message'] = $isEnabled
                ? 'System kolejkowy został włączony.'
                : 'System kolejkowy został wyłączony.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas zmiany ustawień systemu kolejkowego.';
        }

        $this->redirect('/client/queue');
    }

    // Formularz dodawania nowej sali
    public function createRoom() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $this->view('client/queue/create_room', [
            'user' => $user
        ]);
    }

    // Zapisywanie nowej sali
    public function storeRoom() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue');
            return;
        }

        $user = $this->getCurrentUser();
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        $roomNumber = $_POST['room_number'] ?? '';

        if (empty($name)) {
            $this->view('client/queue/create_room', [
                'user' => $user,
                'error' => 'Nazwa sali jest wymagana'
            ]);
            return;
        }

        if ($this->queueSystem->addRoom($user['id'], $name, $description, $doctorId, $roomNumber)) {
            $_SESSION['flash_message'] = 'Sala została dodana pomyślnie.';
            $this->redirect('/client/queue');
        } else {
            $this->view('client/queue/create_room', [
                'user' => $user,
                'error' => 'Wystąpił błąd podczas dodawania sali.'
            ]);
        }
    }

    // Formularz edycji sali
    public function editRoom($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Pobierz informacje o sali
        $stmt = $this->db->prepare("
            SELECT * FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user['id']]);
        $room = $stmt->fetch();

        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }

        $this->view('client/queue/edit_room', [
            'user' => $user,
            'room' => $room
        ]);
    }

    // Aktualizacja sali
    public function updateRoom($id) {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue');
            return;
        }

        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $active = isset($_POST['active']) && $_POST['active'] == 1;
        $doctorId = !empty($_POST['doctor_id']) ? $_POST['doctor_id'] : null;
        $roomNumber = $_POST['room_number'] ?? '';

        if (empty($name)) {
            $this->view('client/queue/edit_room', [
                'user' => $user,
                'error' => 'Nazwa sali jest wymagana',
                'room' => [
                    'id' => $id,
                    'name' => $name,
                    'description' => $description,
                    'active' => $active,
                    'doctor_id' => $doctorId,
                    'room_number' => $roomNumber
                ]
            ]);
            return;
        }

        if ($this->queueSystem->updateRoom($id, $name, $description, $active, $doctorId, $roomNumber)) {
            $_SESSION['flash_message'] = 'Sala została zaktualizowana pomyślnie.';
            $this->redirect('/client/queue');
        } else {
            $this->view('client/queue/edit_room', [
                'user' => $user,
                'error' => 'Wystąpił błąd podczas aktualizacji sali.',
                'room' => [
                    'id' => $id,
                    'name' => $name,
                    'description' => $description,
                    'active' => $active,
                    'doctor_id' => $doctorId,
                    'room_number' => $roomNumber
                ]
            ]);
        }
    }

    // Usuwanie sali
    public function deleteRoom($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }

        if ($this->queueSystem->deleteRoom($id)) {
            $_SESSION['flash_message'] = 'Sala została usunięta pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania sali.';
        }

        $this->redirect('/client/queue');
    }

    // Panel lekarza do zarządzania kolejką w danej sali
    public function doctor($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT * FROM queue_rooms
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);
        $room = $stmt->fetch();

        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }

        // Pobierz aktualną wizytę
        $currentAppointment = $this->queueSystem->getCurrentAppointment($roomId);

        // Pobierz listę oczekujących wizyt
        $waitingAppointments = $this->queueSystem->getWaitingAppointments($roomId);

        $this->view('client/queue/doctor', [
            'user' => $user,
            'room' => $room,
            'currentAppointment' => $currentAppointment,
            'waitingAppointments' => $waitingAppointments
        ]);
    }

    // Wywołanie następnego numeru w kolejce
    public function callNext($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Sala nie istnieje lub nie masz do niej dostępu.']);
            return;
        }

        $nextNumber = $this->queueSystem->callNextAppointment($roomId);

        if ($nextNumber) {
            $this->json([
                'success' => true,
                'number' => $nextNumber['number'],
                'id' => $nextNumber['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak kolejnych numerów w kolejce.']);
        }
    }

    // Generowanie nowego numeru do kolejki
    public function generateNumber() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['error' => 'Method not allowed']);
            return;
        }

        $roomId = $_POST['room_id'] ?? 0;
        $clientId = $_POST['client_id'] ?? 0;

        if (!$roomId || !$clientId) {
            $this->json(['error' => 'Missing required parameters']);
            return;
        }

        $number = $this->queueSystem->generateNumber($clientId, $roomId);

        if ($number) {
            $this->json(['success' => true, 'number' => $number]);
        } else {
            $this->json(['error' => 'Failed to generate number']);
        }
    }

    // Pobieranie informacji o aktualnym stanie kolejki dla wyświetlacza - zorientowane na lekarzy
    public function getQueueStatus($clientId) {
        if (!$this->queueSystem->isEnabled($clientId)) {
            http_response_code(404);
            echo json_encode(['error' => 'System kolejkowy jest wyłączony']);
            return;
        }

        // Pobierz wszystkich aktywnych lekarzy dla klienta
        $doctors = $this->queueSystem->getDoctors($clientId);
        $queueData = [];
        $today = date('Y-m-d');

        foreach ($doctors as $doctor) {
            // Pobierz aktualną wizytę lekarza
            $current = $this->queueSystem->getCurrentAppointmentForDoctor($doctor['id'], $today);
            // Pobierz oczekujące wizyty lekarza
            $waiting = $this->queueSystem->getWaitingAppointmentsForDoctor($doctor['id'], 5, $today);

            // Debug
            if ($doctor['id'] == 3) {
                error_log("Doctor 3 current: " . ($current ? json_encode($current) : 'NULL'));
            }

            // Dodaj lekarza tylko jeśli ma aktualną wizytę
            if ($current !== false && $current !== null) {
                // Pobierz informacje o gabinecie jeśli lekarz ma aktualną wizytę
                $roomData = null;
                if ($current && $current['room_id']) {
                    $roomData = $this->queueSystem->getRoom($current['room_id'], $clientId);
                } elseif (!empty($waiting) && $waiting[0]['room_id']) {
                    $roomData = $this->queueSystem->getRoom($waiting[0]['room_id'], $clientId);
                } elseif ($doctor['default_room_id']) {
                    $roomData = $this->queueSystem->getRoom($doctor['default_room_id'], $clientId);
                }

                $queueData[] = [
                    'doctor' => $doctor,
                    'room' => $roomData,
                    'current' => $current,
                    'waiting' => $waiting
                ];
            }
        }

        header('Content-Type: application/json');
        echo json_encode(['queue' => $queueData]);
    }

    // Pobieranie ostatnich zmian w kolejce dla powiadomień
    public function getQueueChanges($clientId) {
        // Sprawdź czy są nowe zmiany w ciągu ostatnich 10 sekund
        $stmt = $this->db->prepare("
            SELECT
                qa.id,
                qa.doctor_id,
                qa.patient_name,
                qa.appointment_time,
                qa.status,
                qa.started_at,
                qd.first_name,
                qd.last_name,
                qd.photo_url,
                qd.specialization,
                qr.name as room_name
            FROM queue_appointments qa
            JOIN queue_doctors qd ON qa.doctor_id = qd.id
            LEFT JOIN queue_rooms qr ON qa.room_id = qr.id
            WHERE qa.client_id = ?
                AND qa.status = 'current'
                AND qa.started_at > datetime('now', '-10 seconds')
                AND date(qa.appointment_date) = date('now')
            ORDER BY qa.started_at DESC
            LIMIT 1
        ");
        $stmt->execute([$clientId]);
        $recentChange = $stmt->fetch();

        header('Content-Type: application/json');
        echo json_encode(['change' => $recentChange]);
    }

    // Sprawdza czy wizyta jest prawdziwą wizytą pacjenta (nie urlop, nie inne statusy)
    private function isRealAppointment($appointment) {
        if (!$appointment || !isset($appointment['patient_name'])) {
            return false;
        }

        $patientName = strtolower(trim($appointment['patient_name']));

        // Lista słów kluczowych które oznaczają że to nie jest prawdziwa wizyta
        $excludeKeywords = [
            'urlop',
            'urlo',
            'wolne',
            'przerwa',
            'lunch',
            'obiad',
            'szkolenie',
            'konferencja',
            'spotkanie',
            'wizyta',
            'badanie',
            'nieobecny',
            'nieobecna',
            'choroba',
            'l4',
            'zwolnienie'
        ];

        foreach ($excludeKeywords as $keyword) {
            if (strpos($patientName, $keyword) !== false) {
                return false;
            }
        }

        return true;
    }

    // Pomijanie aktualnego numeru w kolejce
    public function skipCurrent($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Sala nie istnieje lub nie masz do niej dostępu.']);
            return;
        }

        $nextNumber = $this->queueSystem->skipCurrentAppointment($roomId);

        if ($nextNumber) {
            $this->json([
                'success' => true,
                'number' => $nextNumber['number'],
                'id' => $nextNumber['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak aktualnego numeru lub kolejnych numerów w kolejce.']);
        }
    }

    // Publiczny widok generowania numerka dla pacjentów
    public function generateNumberPublic($roomId) {
        // Pobierz informacje o sali
        $stmt = $this->db->prepare("
            SELECT r.*, u.id as client_id, u.company_name
            FROM queue_rooms r
            JOIN users u ON r.client_id = u.id
            WHERE r.id = ? AND r.active = 1
        ");
        $stmt->execute([$roomId]);
        $room = $stmt->fetch();

        if (!$room) {
            // Jeśli sala nie istnieje, wyświetl informację o błędzie
            http_response_code(404);
            echo "Sala o podanym identyfikatorze nie istnieje lub jest nieaktywna.";
            return;
        }

        // Sprawdź czy system kolejkowy jest włączony dla tego klienta
        if (!$this->queueSystem->isEnabled($room['client_id'])) {
            echo "System kolejkowy jest obecnie wyłączony.";
            return;
        }

        // Wyświetl stronę generowania numerka
        $this->view('client/queue/generate_number', [
            'room' => $room
        ], false); // Bez domyślnego szablonu
    }

    // ===== ZARZĄDZANIE LEKARZAMI =====

    // Lista lekarzy
    public function doctors() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $doctors = $this->queueSystem->getDoctors($user['id']);
        $rooms = $this->queueSystem->getRooms($user['id']);

        $this->view('client/queue/doctors', [
            'user' => $user,
            'doctors' => $doctors,
            'rooms' => $rooms
        ]);
    }

    // Formularz dodawania lekarza
    public function createDoctor() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Pobierz listę dostępnych gabinetów
        $rooms = $this->queueSystem->getRooms($user['id']);

        $this->view('client/queue/create_doctor', [
            'user' => $user,
            'rooms' => $rooms
        ]);
    }

    // Zapisywanie nowego lekarza
    public function storeDoctor() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/doctors');
            return;
        }

        $user = $this->getCurrentUser();
        $firstName = $_POST['first_name'] ?? '';
        $lastName = $_POST['last_name'] ?? '';
        $specialization = $_POST['specialization'] ?? '';
        $defaultRoomId = !empty($_POST['default_room_id']) ? $_POST['default_room_id'] : null;
        $active = isset($_POST['active']) && $_POST['active'] == 1;

        if (empty($firstName) || empty($lastName)) {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/create_doctor', [
                'user' => $user,
                'rooms' => $rooms,
                'error' => 'Imię i nazwisko są wymagane'
            ]);
            return;
        }

        // Obsługa zdjęcia
        $photoUrl = null;
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = __DIR__ . '/../../uploads/doctors/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            if (in_array($fileExtension, $allowedExtensions)) {
                $fileName = uniqid() . '.' . $fileExtension;
                $filePath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
                    $photoUrl = '/' . $filePath;
                }
            }
        }

        if ($this->queueSystem->addDoctor($user['id'], $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId)) {
            $_SESSION['flash_message'] = 'Lekarz został dodany pomyślnie.';
            $this->redirect('/client/queue/doctors');
        } else {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/create_doctor', [
                'user' => $user,
                'rooms' => $rooms,
                'error' => 'Wystąpił błąd podczas dodawania lekarza.'
            ]);
        }
    }

    // Formularz edycji lekarza
    public function editDoctor($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Pobierz informacje o lekarzu
        $stmt = $this->db->prepare("
            SELECT * FROM queue_doctors
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user['id']]);
        $doctor = $stmt->fetch();

        if (!$doctor) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }

        // Pobierz listę dostępnych gabinetów
        $rooms = $this->queueSystem->getRooms($user['id']);

        $this->view('client/queue/edit_doctor', [
            'user' => $user,
            'doctor' => $doctor,
            'rooms' => $rooms
        ]);
    }

    // Aktualizacja lekarza
    public function updateDoctor($id) {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/doctors');
            return;
        }

        $user = $this->getCurrentUser();

        // Sprawdź czy lekarz należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT * FROM queue_doctors
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$id, $user['id']]);
        $doctor = $stmt->fetch();

        if (!$doctor) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }

        $firstName = $_POST['first_name'] ?? '';
        $lastName = $_POST['last_name'] ?? '';
        $specialization = $_POST['specialization'] ?? '';
        $defaultRoomId = !empty($_POST['default_room_id']) ? $_POST['default_room_id'] : null;
        $active = isset($_POST['active']) && $_POST['active'] == 1;

        if (empty($firstName) || empty($lastName)) {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/edit_doctor', [
                'user' => $user,
                'doctor' => $doctor,
                'rooms' => $rooms,
                'error' => 'Imię i nazwisko są wymagane'
            ]);
            return;
        }

        // Obsługa zdjęcia
        $photoUrl = $doctor['photo_url'];
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = __DIR__ . '/../../uploads/doctors/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            if (in_array($fileExtension, $allowedExtensions)) {
                $fileName = uniqid() . '.' . $fileExtension;
                $filePath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
                    // Usuń stare zdjęcie jeśli istnieje
                    if ($doctor['photo_url'] && file_exists(ltrim($doctor['photo_url'], '/'))) {
                        unlink(ltrim($doctor['photo_url'], '/'));
                    }
                    $photoUrl = '/' . $filePath;
                }
            }
        }

        if ($this->queueSystem->updateDoctor($id, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId)) {
            $_SESSION['flash_message'] = 'Lekarz został zaktualizowany pomyślnie.';
            $this->redirect('/client/queue/doctors');
        } else {
            $rooms = $this->queueSystem->getRooms($user['id']);
            $this->view('client/queue/edit_doctor', [
                'user' => $user,
                'doctor' => $doctor,
                'rooms' => $rooms,
                'error' => 'Wystąpił błąd podczas aktualizacji lekarza.'
            ]);
        }
    }

    // Usuwanie lekarza
    public function deleteDoctor($id) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $doctor = $this->queueSystem->getDoctor($id);

        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }

        if ($this->queueSystem->deleteDoctor($id)) {
            $_SESSION['flash_message'] = 'Lekarz został usunięty pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania lekarza.';
        }

        $this->redirect('/client/queue/doctors');
    }

    // ===== ZARZĄDZANIE WIZYTAMI =====

    // Panel lekarza do zarządzania wizytami
    public function doctorAppointments($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT r.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                   d.photo_url as doctor_photo, d.specialization as doctor_specialization
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.doctor_id = d.id
            WHERE r.id = ? AND r.client_id = ? AND r.active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);
        $room = $stmt->fetch();

        if (!$room) {
            $_SESSION['flash_message'] = 'Sala nie istnieje lub nie masz do niej dostępu.';
            $this->redirect('/client/queue');
            return;
        }

        // Pobierz wybraną datę lub dzisiejszą
        $selectedDate = $_GET['date'] ?? date('Y-m-d');

        // Sprawdź czy data jest poprawna
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $selectedDate)) {
            $selectedDate = date('Y-m-d');
        }

        // Pobierz aktualną wizytę (tylko dla dzisiaj)
        $currentAppointment = null;
        if ($selectedDate === date('Y-m-d')) {
            $currentAppointment = $this->queueSystem->getCurrentAppointment($roomId);
        }

        // Pobierz listę oczekujących wizyt dla wybranej daty
        $waitingAppointments = $this->queueSystem->getWaitingAppointments($roomId, 10, $selectedDate);

        // Pobierz wszystkie wizyty dla wybranej daty
        $allAppointments = $this->queueSystem->getAllAppointments($roomId, $selectedDate);

        // Pobierz listę lekarzy dla tego klienta
        $doctors = $this->queueSystem->getDoctors($user['id']);

        $this->view('client/queue/doctor_appointments', [
            'user' => $user,
            'room' => $room,
            'currentAppointment' => $currentAppointment,
            'waitingAppointments' => $waitingAppointments,
            'allAppointments' => $allAppointments,
            'selectedDate' => $selectedDate,
            'doctors' => $doctors
        ]);
    }

    // Wywołanie następnej wizyty
    public function callNextAppointment($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Sala nie istnieje lub nie masz do niej dostępu.']);
            return;
        }

        $nextAppointment = $this->queueSystem->callNextAppointment($roomId);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment_time' => $nextAppointment['appointment_time'],
                'patient_name' => $nextAppointment['patient_name'],
                'id' => $nextAppointment['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak kolejnych wizyt w kolejce.']);
        }
    }

    // Pomijanie aktualnej wizyty
    public function skipCurrentAppointment($roomId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Sala nie istnieje lub nie masz do niej dostępu.']);
            return;
        }

        $nextAppointment = $this->queueSystem->skipCurrentAppointment($roomId);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment_time' => $nextAppointment['appointment_time'],
                'patient_name' => $nextAppointment['patient_name'],
                'id' => $nextAppointment['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak aktualnej wizyty lub kolejnych wizyt w kolejce.']);
        }
    }

    // Dodawanie nowej wizyty
    public function addAppointment() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['error' => 'Nieprawidłowa metoda']);
            return;
        }

        $user = $this->getCurrentUser();
        $roomId = $_POST['room_id'] ?? null;
        $doctorId = $_POST['doctor_id'] ?? null;
        $appointmentTime = $_POST['appointment_time'] ?? null;
        $appointmentDate = $_POST['appointment_date'] ?? null;
        $patientName = $_POST['patient_name'] ?? '';

        if (!$roomId || !$appointmentTime || !$appointmentDate) {
            $this->json(['error' => 'Brak wymaganych danych']);
            return;
        }

        // Sprawdź czy data nie jest z przeszłości
        if ($appointmentDate < date('Y-m-d')) {
            $this->json(['error' => 'Nie można dodać wizyty w przeszłości']);
            return;
        }

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Sala nie istnieje lub nie masz do niej dostępu']);
            return;
        }

        // Jeśli nie podano lekarza, spróbuj znaleźć domyślnego lekarza dla gabinetu
        if (!$doctorId) {
            $stmt = $this->db->prepare("
                SELECT doctor_id FROM queue_rooms
                WHERE id = ? AND doctor_id IS NOT NULL
            ");
            $stmt->execute([$roomId]);
            $doctorId = $stmt->fetchColumn();
        }

        // Sprawdź czy lekarz należy do tego klienta
        if ($doctorId) {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_doctors
                WHERE id = ? AND client_id = ? AND active = 1
            ");
            $stmt->execute([$doctorId, $user['id']]);

            if ($stmt->fetchColumn() == 0) {
                $this->json(['error' => 'Lekarz nie istnieje lub nieaktywny']);
                return;
            }
        }

        // Sprawdź czy gabinet nie jest już zajęty przez innego lekarza w tym dniu
        if ($doctorId) {
            $stmt = $this->db->prepare("
                SELECT COUNT(*)
                FROM queue_appointments
                WHERE room_id = ? AND appointment_date = ? AND doctor_id != ? AND status IN ('waiting', 'current')
            ");
            $stmt->execute([$roomId, $appointmentDate, $doctorId]);
            $conflicts = $stmt->fetchColumn();

            if ($conflicts > 0) {
                $this->json(['error' => 'Gabinet jest już zajęty przez innego lekarza w tym dniu']);
                return;
            }
        }

        if ($this->queueSystem->addAppointment($user['id'], $roomId, $appointmentTime, $patientName, $appointmentDate, $doctorId)) {
            $this->json(['success' => true]);
        } else {
            $this->json(['error' => 'Nie można dodać wizyty']);
        }
    }

    // Edycja wizyty
    public function editAppointment($appointmentId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $appointment = $this->queueSystem->getAppointment($appointmentId);

        if (!$appointment) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje.';
            $this->redirect('/client/queue');
            return;
        }

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$appointment['room_id'], $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Nie masz dostępu do tej wizyty.';
            $this->redirect('/client/queue');
            return;
        }

        // Pobierz informacje o sali
        $stmt = $this->db->prepare("
            SELECT r.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.doctor_id = d.id
            WHERE r.id = ?
        ");
        $stmt->execute([$appointment['room_id']]);
        $room = $stmt->fetch();

        $this->view('client/queue/edit_appointment', [
            'user' => $user,
            'appointment' => $appointment,
            'room' => $room
        ]);
    }

    // Aktualizacja wizyty
    public function updateAppointment($appointmentId) {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue');
            return;
        }

        $user = $this->getCurrentUser();
        $appointmentTime = $_POST['appointment_time'] ?? null;
        $appointmentDate = $_POST['appointment_date'] ?? null;
        $patientName = $_POST['patient_name'] ?? '';

        if (!$appointmentTime || !$appointmentDate) {
            $_SESSION['flash_message'] = 'Godzina i data wizyty są wymagane.';
            $this->redirect("/client/queue/appointments/edit/{$appointmentId}");
            return;
        }

        // Sprawdź czy data nie jest z przeszłości
        if ($appointmentDate < date('Y-m-d')) {
            $_SESSION['flash_message'] = 'Nie można ustawić daty wizyty w przeszłości.';
            $this->redirect("/client/queue/appointments/edit/{$appointmentId}");
            return;
        }

        $appointment = $this->queueSystem->getAppointment($appointmentId);

        if (!$appointment) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje.';
            $this->redirect('/client/queue');
            return;
        }

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$appointment['room_id'], $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Nie masz dostępu do tej wizyty.';
            $this->redirect('/client/queue');
            return;
        }

        if ($this->queueSystem->updateAppointment($appointmentId, $appointmentTime, $patientName, $appointmentDate)) {
            $_SESSION['flash_message'] = 'Wizyta została zaktualizowana pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas aktualizacji wizyty.';
        }
        $dateParam = $appointmentDate ? ('?date=' . urlencode($appointmentDate)) : '';
        $this->redirect("/client/queue/appointments/{$appointment['room_id']}{$dateParam}");
    }

    // Usuwanie wizyty
    public function deleteAppointment($appointmentId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $appointment = $this->queueSystem->getAppointment($appointmentId);

        if (!$appointment) {
            $_SESSION['flash_message'] = 'Wizyta nie istnieje.';
            $this->redirect('/client/queue');
            return;
        }

        // Sprawdź czy sala należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$appointment['room_id'], $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $_SESSION['flash_message'] = 'Nie masz dostępu do tej wizyty.';
            $this->redirect('/client/queue');
            return;
        }

        if ($this->queueSystem->deleteAppointment($appointmentId)) {
            $_SESSION['flash_message'] = 'Wizyta została usunięta pomyślnie.';
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas usuwania wizyty.';
        }
        $dateParam = isset($appointment['appointment_date']) ? ('?date=' . urlencode($appointment['appointment_date'])) : '';
        $this->redirect("/client/queue/appointments/{$appointment['room_id']}{$dateParam}");
    }

    // Generowanie kodu dostępu dla lekarza
    public function generateAccessCode($doctorId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $doctor = $this->queueSystem->getDoctor($doctorId);

        if (!$doctor || $doctor['client_id'] != $user['id']) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }

        // Generuj kod dostępu
        $accessCode = $this->generateRandomCode();

        // Sprawdź czy kod już istnieje
        $stmt = $this->db->prepare("SELECT id FROM queue_doctors WHERE access_code = ?");
        $stmt->execute([$accessCode]);

        while ($stmt->fetch()) {
            $accessCode = $this->generateRandomCode();
            $stmt->execute([$accessCode]);
        }

        // Zapisz kod dostępu
        $stmt = $this->db->prepare("
            UPDATE queue_doctors 
            SET access_code = ? 
            WHERE id = ?
        ");

        if ($stmt->execute([$accessCode, $doctorId])) {
            $_SESSION['flash_message'] = "Kod dostępu został wygenerowany: $accessCode";
        } else {
            $_SESSION['flash_message'] = 'Wystąpił błąd podczas generowania kodu dostępu.';
        }

        $this->redirect('/client/queue/doctors');
    }

    // Generowanie losowego kodu dostępu
    private function generateRandomCode() {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';

        for ($i = 0; $i < 12; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $code;
    }

    // ===== NOWY SYSTEM KOLEJKI DLA LEKARZY =====

    // Panel lekarza - widok kolejek dla konkretnego lekarza
    public function doctorQueue($doctorId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Pobierz informacje o lekarzu
        $stmt = $this->db->prepare("
            SELECT d.*, r.name as default_room_name, r.room_number as default_room_number
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE d.id = ? AND d.client_id = ?
        ");
        $stmt->execute([$doctorId, $user['id']]);
        $doctor = $stmt->fetch();

        if (!$doctor) {
            $_SESSION['flash_message'] = 'Lekarz nie istnieje lub nie masz do niego dostępu.';
            $this->redirect('/client/queue/doctors');
            return;
        }

        $selectedDate = $_GET['date'] ?? date('Y-m-d');

        // Pobierz aktualną wizytę dla lekarza
        $currentAppointment = $this->queueSystem->getCurrentAppointmentForDoctor($doctorId, $selectedDate);

        // Pobierz listę oczekujących wizyt dla lekarza
        $waitingAppointments = $this->queueSystem->getWaitingAppointmentsForDoctor($doctorId, 10, $selectedDate);

        // Pobierz wszystkie wizyty dla lekarza
        $allAppointments = $this->queueSystem->getAllAppointmentsForDoctor($doctorId, $selectedDate);

        // Pobierz statystyki lekarza
        $doctorStats = $this->queueSystem->getDoctorStats($doctorId, $selectedDate);

        $this->view('client/queue/doctor_queue', [
            'user' => $user,
            'doctor' => $doctor,
            'currentAppointment' => $currentAppointment,
            'waitingAppointments' => $waitingAppointments,
            'allAppointments' => $allAppointments,
            'doctorStats' => $doctorStats,
            'selectedDate' => $selectedDate
        ]);
    }

    // Wywołanie następnej wizyty dla lekarza
    public function callNextAppointmentForDoctor($doctorId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy lekarz należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_doctors
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$doctorId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Lekarz nie istnieje lub nie masz do niego dostępu.']);
            return;
        }

        $date = $_POST['date'] ?? date('Y-m-d');
        $nextAppointment = $this->queueSystem->callNextAppointmentForDoctor($doctorId, $date);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment_time' => $nextAppointment['appointment_time'],
                'patient_name' => $nextAppointment['patient_name'],
                'id' => $nextAppointment['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak kolejnych wizyt w kolejce.']);
        }
    }

    // Pomijanie aktualnej wizyty dla lekarza
    public function skipCurrentAppointmentForDoctor($doctorId) {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        // Sprawdź czy lekarz należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_doctors
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$doctorId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Lekarz nie istnieje lub nie masz do niego dostępu.']);
            return;
        }

        $date = $_POST['date'] ?? date('Y-m-d');
        $nextAppointment = $this->queueSystem->skipCurrentAppointmentForDoctor($doctorId, $date);

        if ($nextAppointment) {
            $this->json([
                'success' => true,
                'appointment_time' => $nextAppointment['appointment_time'],
                'patient_name' => $nextAppointment['patient_name'],
                'id' => $nextAppointment['id']
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Brak aktualnej wizyty lub kolejnych wizyt w kolejce.']);
        }
    }

    // Dodawanie nowej wizyty dla lekarza
    public function addAppointmentForDoctor() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['error' => 'Nieprawidłowa metoda']);
            return;
        }

        $user = $this->getCurrentUser();
        $doctorId = $_POST['doctor_id'] ?? null;
        $roomId = $_POST['room_id'] ?? null;
        $appointmentTime = $_POST['appointment_time'] ?? null;
        $appointmentDate = $_POST['appointment_date'] ?? null;
        $patientName = $_POST['patient_name'] ?? '';

        if (!$doctorId || !$appointmentTime || !$appointmentDate) {
            $this->json(['error' => 'Brak wymaganych danych']);
            return;
        }

        // Sprawdź czy data nie jest z przeszłości
        if ($appointmentDate < date('Y-m-d')) {
            $this->json(['error' => 'Nie można dodać wizyty w przeszłości']);
            return;
        }

        // Sprawdź czy lekarz należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_doctors
            WHERE id = ? AND client_id = ? AND active = 1
        ");
        $stmt->execute([$doctorId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Lekarz nie istnieje lub nieaktywny']);
            return;
        }

        // Jeśli nie podano gabinetu, użyj domyślnego gabinetu lekarza
        if (!$roomId) {
            $stmt = $this->db->prepare("
                SELECT default_room_id FROM queue_doctors
                WHERE id = ?
            ");
            $stmt->execute([$doctorId]);
            $roomId = $stmt->fetchColumn();

            if (!$roomId) {
                $this->json(['error' => 'Lekarz nie ma przypisanego domyślnego gabinetu']);
                return;
            }
        }

        // Sprawdź czy gabinet należy do tego klienta
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM queue_rooms
            WHERE id = ? AND client_id = ?
        ");
        $stmt->execute([$roomId, $user['id']]);

        if ($stmt->fetchColumn() == 0) {
            $this->json(['error' => 'Gabinet nie istnieje lub nie masz do niego dostępu']);
            return;
        }

        // Sprawdź czy lekarz nie ma już wizyty o tej godzinie
        $stmt = $this->db->prepare("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ? AND status IN ('waiting', 'current')
        ");
        $stmt->execute([$doctorId, $appointmentDate, $appointmentTime]);
        $conflicts = $stmt->fetchColumn();

        if ($conflicts > 0) {
            $this->json(['error' => 'Lekarz ma już wizytę o tej godzinie']);
            return;
        }

        if ($this->queueSystem->addAppointment($user['id'], $roomId, $appointmentTime, $patientName, $appointmentDate, $doctorId)) {
            $this->json(['success' => true]);
        } else {
            $this->json(['error' => 'Nie można dodać wizyty']);
        }
    }

    public function rooms() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();
        $rooms = $this->queueSystem->getRooms($user['id']);
        $this->view('client/queue/rooms', [
            'user' => $user,
            'rooms' => $rooms
        ]);
    }

    // ===== IMPORT DANYCH CSV =====

    // Formularz importu CSV
    public function importCsv() {
        $this->requireAuth('client');
        $user = $this->getCurrentUser();

        $this->view('client/queue/import_csv', [
            'user' => $user
        ]);
    }

    // Przetwarzanie pliku CSV - krok 1: analiza pliku
    public function processCsvFile() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/import-csv');
            return;
        }

        $user = $this->getCurrentUser();

        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            $_SESSION['flash_message'] = 'Błąd podczas przesyłania pliku.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        $file = $_FILES['csv_file'];
        $fileName = $file['name'];
        $filePath = $file['tmp_name'];

        // Sprawdź rozszerzenie pliku
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if ($extension !== 'csv') {
            $_SESSION['flash_message'] = 'Plik musi mieć rozszerzenie .csv';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        // Określ separator na podstawie zawartości pliku
        $content = file_get_contents($filePath);
        $separator = $this->detectSeparator($content);

        // Przetwórz plik CSV
        $parsedData = $this->parseCsvFile($filePath, $separator);

        if (empty($parsedData)) {
            $_SESSION['flash_message'] = 'Nie udało się przetworzyć pliku CSV lub plik jest pusty.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        // Filtruj sekcje - zostaw tylko te z wizytami
        $filteredData = [];
        foreach ($parsedData['sections'] as $section) {
            if (!empty($section['appointments'])) {
                $filteredData[] = $section;
            }
        }

        if (empty($filteredData)) {
            $_SESSION['flash_message'] = 'Nie znaleziono żadnych wizyt do zaimportowania w pliku CSV.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        // Zapisz dane w sesji do następnego kroku
        $_SESSION['csv_import_data'] = $filteredData;
        $_SESSION['csv_file_name'] = $fileName;
        $_SESSION['csv_detected_date'] = $parsedData['detected_date'];

        // Pobierz listę lekarzy klienta
        $doctors = $this->queueSystem->getDoctors($user['id']);

        // Pobierz zapisane mapowania lekarzy
        $savedMappings = $this->getSavedDoctorMappings($user['id']);

        // Ustaw domyślną datę - użyj wykrytej daty z pliku lub dzisiejszej
        $defaultDate = $parsedData['detected_date'] ?? date('Y-m-d');

        $this->view('client/queue/import_csv_mapping', [
            'user' => $user,
            'parsedData' => $filteredData,
            'doctors' => $doctors,
            'fileName' => $fileName,
            'savedMappings' => $savedMappings,
            'defaultDate' => $defaultDate
        ]);
    }

    // Wykonanie importu - krok 2: mapowanie i import
    public function executeImport() {
        $this->requireAuth('client');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/client/queue/import-csv');
            return;
        }

        $user = $this->getCurrentUser();

        if (!isset($_SESSION['csv_import_data'])) {
            $_SESSION['flash_message'] = 'Brak danych do importu.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        $parsedData = $_SESSION['csv_import_data'];
        $appointmentDate = $_POST['appointment_date'] ?? '';
        $doctorMappings = $_POST['doctor_mappings'] ?? [];
        $rememberMappings = isset($_POST['remember_mappings']);

        if (empty($appointmentDate)) {
            $_SESSION['flash_message'] = 'Data wizyt jest wymagana.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        // Sprawdź czy data nie jest z przeszłości
        if ($appointmentDate < date('Y-m-d')) {
            $_SESSION['flash_message'] = 'Nie można importować wizyt z przeszłości.';
            $this->redirect('/client/queue/import-csv');
            return;
        }

        $importedCount = 0;
        $errors = [];

        // Pobierz domyślny gabinet dla każdego lekarza
        $doctors = $this->queueSystem->getDoctors($user['id']);
        $doctorRooms = [];
        foreach ($doctors as $doctor) {
            $doctorRooms[$doctor['id']] = $doctor['default_room_id'];
        }

        foreach ($parsedData as $sectionIndex => $section) {
            $doctorName = $section['doctor_name'];
            $appointments = $section['appointments'];

            // Znajdź lekarza na podstawie mapowania
            $doctorId = null;
            foreach ($doctorMappings as $mapping) {
                if ($mapping['csv_doctor'] === $doctorName && !empty($mapping['system_doctor'])) {
                    $doctorId = $mapping['system_doctor'];
                    break;
                }
            }

            if (!$doctorId) {
                $errors[] = "Brak mapowania dla lekarza: $doctorName";
                continue;
            }

            // Sprawdź czy lekarz ma przypisany gabinet
            $roomId = $doctorRooms[$doctorId];
            if (!$roomId) {
                $errors[] = "Lekarz $doctorName nie ma przypisanego domyślnego gabinetu";
                continue;
            }

            foreach ($appointments as $appointment) {
                $appointmentTime = $appointment['time'];
                $patientName = $appointment['patient'];

                // Sprawdź czy godzina jest poprawna
                if (empty($appointmentTime) || $appointmentTime === 'poza kol.' || $appointmentTime === 'Powtórka Rp.') {
                    continue; // Pomijamy wizyty poza kolejką
                }

                // Sprawdź czy lekarz nie ma już wizyty o tej godzinie
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM queue_appointments
                    WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
                    AND status IN ('waiting', 'current')
                ");
                $stmt->execute([$doctorId, $appointmentDate, $appointmentTime]);

                if ($stmt->fetchColumn() > 0) {
                    $errors[] = "Lekarz $doctorName ma już wizytę o godzinie $appointmentTime";
                    continue;
                }

                // Dodaj wizytę
                if ($this->queueSystem->addAppointment($user['id'], $roomId, $appointmentTime, $patientName, $appointmentDate, $doctorId)) {
                    $importedCount++;
                } else {
                    $errors[] = "Błąd podczas dodawania wizyty: $patientName o $appointmentTime";
                }
            }
        }

        // Zapisz mapowania jeśli użytkownik zaznaczył checkbox
        if ($rememberMappings) {
            $this->saveDoctorMappings($user['id'], $doctorMappings);
        }

        // Wyczyść dane z sesji
        unset($_SESSION['csv_import_data']);
        unset($_SESSION['csv_file_name']);

        // Przygotuj komunikat
        $message = "Zaimportowano $importedCount wizyt.";
        if (!empty($errors)) {
            $message .= " Wystąpiły błędy: " . implode(', ', $errors);
        }

        $_SESSION['flash_message'] = $message;
        $this->redirect('/client/queue');
    }

    // Wykrywanie separatora w pliku CSV
    private function detectSeparator($content) {
        // Konwertuj kodowanie z Windows-1250 na UTF-8
        $content = $this->convertEncoding($content);

        $lines = explode("\n", $content);
        if (empty($lines)) return ',';

        $firstLine = $lines[0];
        $commaCount = substr_count($firstLine, ',');
        $pipeCount = substr_count($firstLine, '|');
        $semicolonCount = substr_count($firstLine, ';');

        if ($pipeCount > $commaCount && $pipeCount > $semicolonCount) {
            return '|';
        } elseif ($semicolonCount > $commaCount && $semicolonCount > $pipeCount) {
            return ';';
        } else {
            return ',';
        }
    }

    // Konwersja kodowania z Windows-1250 na UTF-8
    private function convertEncoding($content) {
        // Sprawdź czy to już UTF-8 (proste sprawdzenie)
        if ($this->isUtf8($content)) {
            return $content;
        }

        // Próbuj konwersję z Windows-1250 na UTF-8
        $converted = iconv('Windows-1250', 'UTF-8//IGNORE', $content);
        if ($converted !== false) {
            return $converted;
        }

        // Próbuj z CP1250
        $converted = iconv('CP1250', 'UTF-8//IGNORE', $content);
        if ($converted !== false) {
            return $converted;
        }

        // Próbuj z ISO-8859-2
        $converted = iconv('ISO-8859-2', 'UTF-8//IGNORE', $content);
        if ($converted !== false) {
            return $converted;
        }

        // Jeśli nic nie zadziałało, zwróć oryginalny content
        return $content;
    }

    // Proste sprawdzenie czy string jest UTF-8
    private function isUtf8($string) {
        return preg_match('//u', $string);
    }

    // Parsowanie pliku CSV
    private function parseCsvFile($filePath, $separator) {
        $content = file_get_contents($filePath);

        // Konwertuj kodowanie
        $content = $this->convertEncoding($content);

        $lines = explode("\n", $content);

        $sections = [];
        $currentSection = null;
        $currentDoctor = null;
        $detectedDate = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Sprawdź czy to nagłówek z datą
            if (preg_match('/na dzień (\d{4}-\d{2}-\d{2})/', $line, $matches)) {
                $detectedDate = $matches[1];
                continue;
            }

            // Sprawdź czy to sekcja lekarza
            if (preg_match('/Lekarz:\s*(.+)/', $line, $matches)) {
                if ($currentSection) {
                    $sections[] = $currentSection;
                }

                $currentDoctor = trim($matches[1]);
                // Usuń dodatkowe przecinki i znaki z nazwy lekarza
                $currentDoctor = preg_replace('/[,]+$/', '', $currentDoctor);
                $currentDoctor = trim($currentDoctor);

                $currentSection = [
                    'doctor_name' => $currentDoctor,
                    'appointments' => []
                ];
                continue;
            }

            // Sprawdź czy to wiersz z danymi (zaczyna się od liczby)
            if (preg_match('/^(\d+)/', $line)) {
                $fields = str_getcsv($line, $separator);

                if (count($fields) >= 5) {
                    $appointmentTime = trim($fields[2] ?? ''); // Godz.zaplanowana
                    $patientName = trim($fields[4] ?? ''); // Pacjent

                    // Sprawdź czy godzina jest poprawna i nie jest pusta
                    if (
                        !empty($appointmentTime) &&
                        $appointmentTime !== 'poza kol.' &&
                        $appointmentTime !== 'Powtórka Rp.' &&
                        $this->isValidTime($appointmentTime)
                    ) {

                        if ($currentSection) {
                            $currentSection['appointments'][] = [
                                'time' => $appointmentTime,
                                'patient' => $patientName
                            ];
                        }
                    }
                }
            }
        }

        // Dodaj ostatnią sekcję
        if ($currentSection) {
            $sections[] = $currentSection;
        }

        return [
            'sections' => $sections,
            'detected_date' => $detectedDate
        ];
    }

    // Sprawdzanie czy godzina jest poprawna
    private function isValidTime($time) {
        // Sprawdź czy to format HH:MM lub HH:MM:SS
        if (preg_match('/^(\d{1,2}):(\d{2})(:(\d{2}))?$/', $time, $matches)) {
            $hour = (int)$matches[1];
            $minute = (int)$matches[2];

            return $hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59;
        }

        return false;
    }

    // Zapisywanie mapowań lekarzy
    private function saveDoctorMappings($clientId, $mappings) {
        // Usuń stare mapowania
        $stmt = $this->db->prepare("DELETE FROM csv_doctor_mappings WHERE client_id = ?");
        $stmt->execute([$clientId]);

        // Zapisz nowe mapowania
        foreach ($mappings as $mapping) {
            if (!empty($mapping['csv_doctor']) && !empty($mapping['system_doctor'])) {
                $stmt = $this->db->prepare("
                    INSERT INTO csv_doctor_mappings (client_id, csv_doctor_name, system_doctor_id)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$clientId, $mapping['csv_doctor'], $mapping['system_doctor']]);
            }
        }
    }

    // Pobieranie zapisanych mapowań lekarzy
    private function getSavedDoctorMappings($clientId) {
        $stmt = $this->db->prepare("
            SELECT csv_doctor_name, system_doctor_id 
            FROM csv_doctor_mappings 
            WHERE client_id = ?
        ");
        $stmt->execute([$clientId]);

        $mappings = [];
        while ($row = $stmt->fetch()) {
            $mappings[$row['csv_doctor_name']] = $row['system_doctor_id'];
        }

        return $mappings;
    }
}
