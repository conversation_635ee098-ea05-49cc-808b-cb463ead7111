<?php

class Doctor {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    // Pobiera wszystkich lekarzy dla danego klienta
    public function getByClientId($clientId) {
        $stmt = $this->db->prepare("
            SELECT d.*, r.name as default_room_name, r.room_number as default_room_number
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE d.client_id = ? AND d.active = 1
            ORDER BY d.last_name, d.first_name
        ");
        $stmt->execute([$clientId]);
        return $stmt->fetchAll();
    }

    // Pobiera lekarza po ID
    public function getById($doctorId) {
        $stmt = $this->db->prepare("SELECT * FROM queue_doctors WHERE id = ?");
        $stmt->execute([$doctorId]);
        return $stmt->fetch();
    }

    // Dodaje nowego lekarza
    public function create($clientId, $firstName, $lastName, $specialization = '', $photoUrl = '', $active = true, $defaultRoomId = null) {
        // Jeśli nie podano zdjęcia, ustaw domyślne
        if (empty($photoUrl)) {
            $photoUrl = 'https://sonokard.pl/fileadmin/_processed_/c/4/csm_avatar_32d3b4fed0.png';
        }

        $stmt = $this->db->prepare("
            INSERT INTO queue_doctors (client_id, first_name, last_name, specialization, photo_url, active, default_room_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        return $stmt->execute([$clientId, $firstName, $lastName, $specialization, $photoUrl, $active ? 1 : 0, $defaultRoomId]);
    }

    // Aktualizuje lekarza
    public function update($doctorId, $firstName, $lastName, $specialization, $photoUrl, $active, $defaultRoomId = null) {
        // Jeśli nie podano zdjęcia, ustaw domyślne
        if (empty($photoUrl)) {
            $photoUrl = 'https://sonokard.pl/fileadmin/_processed_/c/4/csm_avatar_32d3b4fed0.png';
        }

        $stmt = $this->db->prepare("
            UPDATE queue_doctors
            SET first_name = ?, last_name = ?, specialization = ?, photo_url = ?, active = ?, default_room_id = ?
            WHERE id = ?
        ");
        return $stmt->execute([$firstName, $lastName, $specialization, $photoUrl, $active ? 1 : 0, $defaultRoomId, $doctorId]);
    }

    // Usuwa lekarza
    public function delete($doctorId) {
        $stmt = $this->db->prepare("DELETE FROM queue_doctors WHERE id = ?");
        return $stmt->execute([$doctorId]);
    }
}
