<?php

class Appointment {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    // Pobiera aktualną wizytę dla danej sali
    public function getCurrentByRoomId($roomId) {
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE room_id = ? AND status = 'current'
            ORDER BY started_at DESC
            LIMIT 1
        ");
        $stmt->execute([$roomId]);
        return $stmt->fetch();
    }

    // Pobiera listę oczekujących wizyt dla danej sali
    public function getWaitingByRoomId($roomId, $limit = 5, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE room_id = ? AND appointment_date = ? AND status = 'waiting'
            ORDER BY appointment_time ASC
            LIMIT ?
        ");
        $stmt->execute([$roomId, $date, $limit]);
        return $stmt->fetchAll();
    }

    // Pobiera wszystkie wizyty dla danej sali (wszystkie statusy)
    public function getAllByRoomId($roomId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE room_id = ? AND appointment_date = ?
            ORDER BY appointment_time ASC
        ");
        $stmt->execute([$roomId, $date]);
        return $stmt->fetchAll();
    }

    // Pobiera wizytę po ID
    public function getById($appointmentId) {
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE id = ?
        ");
        $stmt->execute([$appointmentId]);
        return $stmt->fetch();
    }

    // Dodaje nową wizytę
    public function create($clientId, $roomId, $appointmentTime, $patientName = '', $appointmentDate = null, $doctorId = null) {
        if (!$appointmentDate) {
            $appointmentDate = date('Y-m-d');
        }

        // Jeśli nie podano lekarza, spróbuj znaleźć domyślnego lekarza dla gabinetu
        if (!$doctorId) {
            $stmt = $this->db->prepare("
                SELECT doctor_id FROM queue_rooms
                WHERE id = ? AND doctor_id IS NOT NULL
            ");
            $stmt->execute([$roomId]);
            $doctorId = $stmt->fetchColumn();
        }

        // Łączymy datę z godziną dla created_at
        $appointmentDateTime = $appointmentDate . ' ' . $appointmentTime;

        $stmt = $this->db->prepare("
            INSERT INTO queue_appointments (client_id, room_id, doctor_id, appointment_time, appointment_date, patient_name, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 'waiting', ?)
        ");
        return $stmt->execute([$clientId, $roomId, $doctorId, $appointmentTime, $appointmentDate, $patientName, $appointmentDateTime]);
    }

    // Dodaje nową wizytę z danymi z importu
    public function createFromImport($data) {
        try {
            // Sprawdź czy kolumna external_id istnieje, jeśli nie - dodaj ją
            $this->ensureExternalIdColumn();

            $appointmentDateTime = $data['appointment_date'] . ' ' . $data['appointment_time'];

            // Sprawdź czy lekarz istnieje
            if (!$data['doctor_id']) {
                error_log("KtoOstatni: Brak doctor_id w danych wizyty");
                return false;
            }

            // Znajdź domyślny pokój dla lekarza (opcjonalnie)
            $roomId = $this->findDefaultRoomForDoctor($data['client_id'], $data['doctor_id']);

            // Jeśli nie ma pokoju, ustaw NULL - wizyta będzie przypisana tylko do lekarza
            if (!$roomId) {
                error_log("KtoOstatni: Brak pokoju dla lekarza {$data['doctor_id']}, tworzę wizytę bez pokoju");
                $roomId = null;
            }

            error_log("KtoOstatni: Tworzę wizytę - Klient: {$data['client_id']}, Pokój: " . ($roomId ?: 'NULL') . ", Lekarz: {$data['doctor_id']}, Pacjent: {$data['patient_name']}");

            $stmt = $this->db->prepare("
                INSERT INTO queue_appointments (
                    client_id, room_id, doctor_id, appointment_time, appointment_date,
                    patient_name, status, created_at, external_id, service_name,
                    end_time, office, type
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $result = $stmt->execute([
                $data['client_id'],
                $roomId,
                $data['doctor_id'],
                $data['appointment_time'],
                $data['appointment_date'],
                $data['patient_name'],
                $data['status'] ?? 'waiting',
                $appointmentDateTime,
                $data['external_id'] ?? null,
                $data['service_name'] ?? '',
                $data['end_time'] ?? null,
                $data['office'] ?? '',
                $data['type'] ?? 'wizyta'
            ]);

            if ($result) {
                $appointmentId = $this->db->lastInsertId();
                error_log("KtoOstatni: Wizyta utworzona pomyślnie z ID: {$appointmentId}");
            } else {
                error_log("KtoOstatni: Błąd tworzenia wizyty: " . implode(', ', $stmt->errorInfo()));
            }

            return $result;
        } catch (Exception $e) {
            error_log("KtoOstatni: Wyjątek podczas tworzenia wizyty: " . $e->getMessage());
            return false;
        }
    }

    // Aktualizuje wizytę z danymi z importu
    public function updateFromImport($appointmentId, $data) {
        $this->ensureExternalIdColumn();

        $stmt = $this->db->prepare("
            UPDATE queue_appointments SET
                appointment_time = ?, appointment_date = ?, patient_name = ?,
                doctor_id = ?, service_name = ?, end_time = ?, office = ?, type = ?
            WHERE id = ?
        ");

        return $stmt->execute([
            $data['appointment_time'],
            $data['appointment_date'],
            $data['patient_name'],
            $data['doctor_id'],
            $data['service_name'] ?? '',
            $data['end_time'] ?? null,
            $data['office'] ?? '',
            $data['type'] ?? 'wizyta',
            $appointmentId
        ]);
    }

    // Sprawdza czy kolumny dla importu istnieją i dodaje je jeśli nie
    private function ensureExternalIdColumn() {
        try {
            // Sprawdź czy kolumna external_id istnieje
            $stmt = $this->db->prepare("PRAGMA table_info(queue_appointments)");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $hasExternalId = false;
            $hasServiceName = false;
            $hasEndTime = false;
            $hasOffice = false;
            $hasType = false;

            foreach ($columns as $column) {
                if ($column['name'] === 'external_id') $hasExternalId = true;
                if ($column['name'] === 'service_name') $hasServiceName = true;
                if ($column['name'] === 'end_time') $hasEndTime = true;
                if ($column['name'] === 'office') $hasOffice = true;
                if ($column['name'] === 'type') $hasType = true;
            }

            // Dodaj brakujące kolumny
            if (!$hasExternalId) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN external_id VARCHAR(100)");
            }
            if (!$hasServiceName) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN service_name VARCHAR(200)");
            }
            if (!$hasEndTime) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN end_time TIME");
            }
            if (!$hasOffice) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN office VARCHAR(200)");
            }
            if (!$hasType) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN type VARCHAR(50) DEFAULT 'wizyta'");
            }
        } catch (Exception $e) {
            // Ignoruj błędy - kolumny prawdopodobnie już istnieją
        }
    }

    // Znajdź domyślny pokój dla lekarza
    private function findDefaultRoomForDoctor($clientId, $doctorId) {
        // Najpierw sprawdź czy lekarz ma przypisany domyślny pokój
        $stmt = $this->db->prepare("
            SELECT default_room_id FROM queue_doctors
            WHERE id = ? AND client_id = ? AND default_room_id IS NOT NULL
        ");
        $stmt->execute([$doctorId, $clientId]);
        $defaultRoomId = $stmt->fetchColumn();

        if ($defaultRoomId) {
            return $defaultRoomId;
        }

        // Jeśli nie ma domyślnego pokoju, znajdź pierwszy dostępny pokój dla klienta
        $stmt = $this->db->prepare("
            SELECT id FROM queue_rooms
            WHERE client_id = ? AND active = 1
            ORDER BY room_number
            LIMIT 1
        ");
        $stmt->execute([$clientId]);
        $roomId = $stmt->fetchColumn();

        // Jeśli nie ma żadnych pokoi, utwórz domyślny
        if (!$roomId) {
            $stmt = $this->db->prepare("
                INSERT INTO queue_rooms (client_id, name, room_number, active)
                VALUES (?, 'Pokój domyślny', '1', 1)
            ");
            $stmt->execute([$clientId]);
            $roomId = $this->db->lastInsertId();
        }

        return $roomId;
    }

    // Aktualizuje wizytę
    public function update($appointmentId, $appointmentTime, $patientName, $appointmentDate = null, $doctorId = null) {
        if (!$appointmentDate) {
            $appointmentDate = date('Y-m-d');
        }

        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET appointment_time = ?, appointment_date = ?, patient_name = ?, doctor_id = ?
            WHERE id = ?
        ");
        return $stmt->execute([$appointmentTime, $appointmentDate, $patientName, $doctorId, $appointmentId]);
    }

    // Usuwa wizytę
    public function delete($appointmentId) {
        $stmt = $this->db->prepare("
            DELETE FROM queue_appointments
            WHERE id = ?
        ");
        return $stmt->execute([$appointmentId]);
    }

    // Pobiera wizytę po external_id
    public function getByExternalId($clientId, $externalId) {
        $this->ensureExternalIdColumn();

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE client_id = ? AND external_id = ?
        ");
        $stmt->execute([$clientId, $externalId]);
        return $stmt->fetch();
    }

    // Pobiera wszystkie wizyty dla klienta w określonym dniu
    public function getAllByClientAndDate($clientId, $date) {
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE client_id = ? AND appointment_date = ?
            ORDER BY appointment_time ASC
        ");
        $stmt->execute([$clientId, $date]);
        return $stmt->fetchAll();
    }

    // Pobiera wszystkie wizyty z external_id dla klienta w określonym dniu
    public function getAllWithExternalIdByClientAndDate($clientId, $date) {
        $this->ensureExternalIdColumn();

        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE client_id = ? AND appointment_date = ? AND external_id IS NOT NULL
            ORDER BY appointment_time ASC
        ");
        $stmt->execute([$clientId, $date]);
        return $stmt->fetchAll();
    }

    // Usuwa wizytę po external_id
    public function deleteByExternalId($clientId, $externalId) {
        $this->ensureExternalIdColumn();

        $stmt = $this->db->prepare("
            DELETE FROM queue_appointments
            WHERE client_id = ? AND external_id = ?
        ");
        return $stmt->execute([$clientId, $externalId]);
    }

    // Usuwa wizyty które nie są w podanej liście external_id dla danego dnia
    // UWAGA: Usuwa tylko wizyty z external_id - nie dotyka wizyt utworzonych ręcznie
    public function deleteNotInExternalIds($clientId, $date, $externalIds) {
        $this->ensureExternalIdColumn();

        if (empty($externalIds)) {
            // Jeśli lista jest pusta, usuń wszystkie wizyty z external_id dla tego dnia
            // BEZPIECZEŃSTWO: Usuwa tylko wizyty z external_id (z importu)
            $stmt = $this->db->prepare("
                DELETE FROM queue_appointments
                WHERE client_id = ? AND appointment_date = ? AND external_id IS NOT NULL AND external_id != ''
            ");
            return $stmt->execute([$clientId, $date]);
        }

        // Stwórz placeholders dla IN clause
        $placeholders = str_repeat('?,', count($externalIds) - 1) . '?';

        $stmt = $this->db->prepare("
            DELETE FROM queue_appointments
            WHERE client_id = ? AND appointment_date = ? AND external_id IS NOT NULL AND external_id != ''
            AND external_id NOT IN ($placeholders)
        ");

        $params = array_merge([$clientId, $date], $externalIds);
        return $stmt->execute($params);
    }

    // Pobiera liczbę usuniętych wizyt (dla statystyk)
    // UWAGA: Liczy tylko wizyty z external_id - nie dotyka wizyt utworzonych ręcznie
    public function countDeletedAppointments($clientId, $date, $externalIds) {
        $this->ensureExternalIdColumn();

        if (empty($externalIds)) {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_appointments
                WHERE client_id = ? AND appointment_date = ? AND external_id IS NOT NULL AND external_id != ''
            ");
            $stmt->execute([$clientId, $date]);
        } else {
            $placeholders = str_repeat('?,', count($externalIds) - 1) . '?';
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM queue_appointments
                WHERE client_id = ? AND appointment_date = ? AND external_id IS NOT NULL AND external_id != ''
                AND external_id NOT IN ($placeholders)
            ");
            $params = array_merge([$clientId, $date], $externalIds);
            $stmt->execute($params);
        }

        return $stmt->fetchColumn();
    }

    // Wywołuje następną wizytę w kolejce
    public function callNext($roomId) {
        // Najpierw oznacz aktualną wizytę jako zakończoną
        $currentAppointment = $this->getCurrentByRoomId($roomId);
        if ($currentAppointment) {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'completed', completed_at = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);
        }

        // Wybierz najwcześniejszą oczekującą wizytę
        $stmt = $this->db->prepare("
            SELECT * FROM queue_appointments
            WHERE room_id = ? AND status = 'waiting'
            ORDER BY appointment_time ASC
            LIMIT 1
        ");
        $stmt->execute([$roomId]);
        $nextAppointment = $stmt->fetch();

        if (!$nextAppointment) {
            return false; // Brak kolejnych wizyt
        }

        // Ustaw tę wizytę jako aktualną
        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET status = 'current', started_at = datetime('now')
            WHERE id = ?
        ");

        if ($stmt->execute([$nextAppointment['id']])) {
            return $nextAppointment;
        }

        return false;
    }

    // Pomija aktualną wizytę w kolejce
    public function skipCurrent($roomId) {
        $currentAppointment = $this->getCurrentByRoomId($roomId);
        if (!$currentAppointment) {
            return false;
        }

        $stmt = $this->db->prepare("
            UPDATE queue_appointments
            SET status = 'waiting'
            WHERE id = ?
        ");

        if (!$stmt->execute([$currentAppointment['id']])) {
            return false;
        }

        return $this->callNext($roomId);
    }
}
