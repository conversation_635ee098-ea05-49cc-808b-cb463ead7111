{"exportDate": "2025-08-27T14:00:00.000Z", "syncCode": "igab1234567890123", "syncData": {"days": [{"date": "2025-08-27", "doctors": [{"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": []}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": []}, {"doctorId": "72", "doctorName": "ginekolog dr <PERSON>-<PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "105", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "114", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "test_appointment_1", "patientFirstName": "<PERSON>", "patientLastName": "Testowa", "appointmentStart": "09:00", "appointmentEnd": "09:30", "appointmentDuration": 30}]}, {"doctorId": "129", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "test_appointment_2", "patientFirstName": "Jan", "patientLastName": "Testowy", "appointmentStart": "10:00", "appointmentEnd": "10:30", "appointmentDuration": 30}]}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "200", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "201", "doctorName": "ginekolog dr An<PERSON> Walaszek-Gruszka", "appointments": []}, {"doctorId": "202", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "203", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "204", "doctorName": "ginekolog dr Agnieszka Tyszko-Tymińska", "appointments": []}]}]}}