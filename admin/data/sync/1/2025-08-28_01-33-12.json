{"exportDate": "2025-08-28T01:33:12.155Z", "syncCode": "igab1234567890123", "syncData": {"days": [{"date": "2025-08-28", "doctors": [{"doctorId": "**********", "doctorName": "dermatolog dr <PERSON>-Wójcik", "appointments": []}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "105025", "patientFirstName": "Weronika", "patientLastName": "Sodel", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "107446", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Szmytka", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}]}, {"doctorId": "43", "doctorName": "genetyk dr n.med. <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "105", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "114", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "104624", "patientFirstName": "Dominika", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "104653", "patientFirstName": "Agata", "patientLastName": "Baranowska", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "104666", "patientFirstName": "Natalia", "patientLastName": "Vasylevska", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": "104681", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Piesiak", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "104708", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "107097", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Rakowska", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "107118", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "107120", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "11:20", "appointmentEnd": "11:40", "appointmentDuration": 20}, {"appointmentId": "107249", "patientFirstName": "<PERSON>", "patientLastName": "Trojnar-Majchrzyk", "appointmentStart": "11:40", "appointmentEnd": "12:00", "appointmentDuration": 20}, {"appointmentId": "107170", "patientFirstName": "Agata", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "12:00", "appointmentEnd": "12:20", "appointmentDuration": 20}, {"appointmentId": "107501", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:20", "appointmentEnd": "12:40", "appointmentDuration": 20}, {"appointmentId": "108396", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "103083", "patientFirstName": "<PERSON>", "patientLastName": "Laskowska-Wojtaszek", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "107700", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "107723", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Modzelewska", "appointmentStart": "13:40", "appointmentEnd": "14:00", "appointmentDuration": 20}]}, {"doctorId": "129", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "72", "doctorName": "ginekolog dr <PERSON>-<PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": [{"appointmentId": "104468", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "104673", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON>ę<PERSON>a", "appointmentStart": "09:20", "appointmentEnd": "10:00", "appointmentDuration": 40}, {"appointmentId": "104694", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "104714", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Rajca", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "105024", "patientFirstName": "Weronika", "patientLastName": "Sodel", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "105167", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Laskowska", "appointmentStart": "11:00", "appointmentEnd": "11:50", "appointmentDuration": 50}, {"appointmentId": "105320", "patientFirstName": "<PERSON>", "patientLastName": "Ku<PERSON><PERSON>", "appointmentStart": "11:50", "appointmentEnd": "12:10", "appointmentDuration": 20}, {"appointmentId": "108510", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "12:10", "appointmentEnd": "12:30", "appointmentDuration": 20}, {"appointmentId": "108511", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "12:30", "appointmentEnd": "12:50", "appointmentDuration": 20}, {"appointmentId": "105712", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Włodarczyk", "appointmentStart": "12:50", "appointmentEnd": "13:10", "appointmentDuration": 20}, {"appointmentId": "108467", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "13:10", "appointmentEnd": "13:30", "appointmentDuration": 20}, {"appointmentId": "108233", "patientFirstName": "Agata", "patientLastName": "Górny", "appointmentStart": "13:30", "appointmentEnd": "13:50", "appointmentDuration": 20}, {"appointmentId": "105840", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "13:50", "appointmentEnd": "14:10", "appointmentDuration": 20}, {"appointmentId": "108528", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Homska", "appointmentStart": "14:10", "appointmentEnd": "14:30", "appointmentDuration": 20}, {"appointmentId": "106121", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Blecharczyk", "appointmentStart": "14:30", "appointmentEnd": "15:00", "appointmentDuration": 30}, {"appointmentId": "105710", "patientFirstName": "Weronika", "patientLastName": "Poślednik", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "105314", "patientFirstName": "Mariana", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "108500", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "105825", "patientFirstName": "<PERSON>", "patientLastName": "Bieniawska", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "107860", "patientFirstName": "Emilia", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "108017", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "108537", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Grzesińska", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "108057", "patientFirstName": "Weronika", "patientLastName": "Sodel", "appointmentStart": "17:25", "appointmentEnd": "17:30", "appointmentDuration": 5}, {"appointmentId": "108499", "patientFirstName": "Katarzyna", "patientLastName": "Stępniowska", "appointmentStart": "17:40", "appointmentEnd": "17:50", "appointmentDuration": 10}]}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": [{"appointmentId": "107907", "patientFirstName": "<PERSON>", "patientLastName": "Gruszczyńska-Marszałkowska", "appointmentStart": "08:40", "appointmentEnd": "09:00", "appointmentDuration": 20}, {"appointmentId": "104398", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "104679", "patientFirstName": "Pol<PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "106396", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Malczyńska-Jurusz", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": "107844", "patientFirstName": "Katarzyna", "patientLastName": "DUNYCZ", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "107880", "patientFirstName": "<PERSON>", "patientLastName": "Czekaj", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "106641", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "106651", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "106654", "patientFirstName": "Magdalena", "patientLastName": "Marchewka", "appointmentStart": "11:20", "appointmentEnd": "11:40", "appointmentDuration": 20}, {"appointmentId": "106729", "patientFirstName": "Aldona", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "11:40", "appointmentEnd": "12:00", "appointmentDuration": 20}, {"appointmentId": "106915", "patientFirstName": "Magdalena", "patientLastName": "Glińska", "appointmentStart": "12:00", "appointmentEnd": "12:20", "appointmentDuration": 20}, {"appointmentId": "106921", "patientFirstName": "Magdalena", "patientLastName": "Dobrudzka", "appointmentStart": "12:20", "appointmentEnd": "12:40", "appointmentDuration": 20}, {"appointmentId": "107110", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "107361", "patientFirstName": "<PERSON>", "patientLastName": "Sudorowska", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "107620", "patientFirstName": "Olimpia", "patientLastName": "Wieruszewska", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "108342", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "13:40", "appointmentEnd": "14:00", "appointmentDuration": 20}]}, {"doctorId": "83", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "107746", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "108530", "patientFirstName": "MONIKA", "patientLastName": "FURMAŃSKA", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "107740", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Wodo-Nowakowska", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "107079", "patientFirstName": "Weronika", "patientLastName": "Ściubidło", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "108469", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Ligorowska", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": "107337", "patientFirstName": "<PERSON>", "patientLastName": "Krzypkowska", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "108407", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": "108257", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Stępień", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "108037", "patientFirstName": "<PERSON>", "patientLastName": "Smolarczyk smolińska", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}, {"appointmentId": "107405", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "19:00", "appointmentEnd": "19:40", "appointmentDuration": 40}, {"appointmentId": "108393", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Ku<PERSON>k", "appointmentStart": "19:40", "appointmentEnd": "20:00", "appointmentDuration": 20}, {"appointmentId": "108352", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Jakubowska", "appointmentStart": "20:20", "appointmentEnd": "20:25", "appointmentDuration": 5}]}, {"doctorId": "19", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "82", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "110", "doctorName": "ginekolog dziecięcy dr Agnieszka Tyszko-Tymińska", "appointments": []}, {"doctorId": "**********", "doctorName": "hepa<PERSON><PERSON>, specjalista chorób zakaźnych dr <PERSON><PERSON>", "appointments": []}]}]}}