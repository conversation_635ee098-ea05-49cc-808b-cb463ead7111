{"exportDate": "2025-08-26T16:36:30.593Z", "syncCode": "igab000000000001", "syncData": {"days": [{"date": "2025-08-26", "doctors": [{"doctorId": "43", "doctorName": "genetyk dr n.med. <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "104852", "patientFirstName": "BLOKADA", "patientLastName": "BLOKADA", "appointmentStart": "16:00", "appointmentEnd": "19:00", "appointmentDuration": 180}]}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": [{"appointmentId": "106689", "patientFirstName": "Dominika", "patientLastName": "Stępień", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "103403", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "103920", "patientFirstName": "<PERSON>", "patientLastName": "Ścisłowska", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": "103066", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Szczepańska", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "104947", "patientFirstName": "Natalia", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "104555", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Krzemieniewska", "appointmentStart": "10:40", "appointmentEnd": "11:10", "appointmentDuration": 30}, {"appointmentId": "104880", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "11:10", "appointmentEnd": "11:30", "appointmentDuration": 20}, {"appointmentId": "108314", "patientFirstName": "Wiktoria", "patientLastName": "Szponarska", "appointmentStart": "11:30", "appointmentEnd": "11:50", "appointmentDuration": 20}, {"appointmentId": "105187", "patientFirstName": "Paul<PERSON>", "patientLastName": "Czech", "appointmentStart": "12:10", "appointmentEnd": "12:30", "appointmentDuration": 20}, {"appointmentId": "103689", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "105327", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Dębska-Grzelak", "appointmentStart": "13:50", "appointmentEnd": "14:20", "appointmentDuration": 30}, {"appointmentId": "108253", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Świsulska", "appointmentStart": "14:20", "appointmentEnd": "14:40", "appointmentDuration": 20}, {"appointmentId": "108319", "patientFirstName": "<PERSON>", "patientLastName": "Klimza", "appointmentStart": "14:40", "appointmentEnd": "15:00", "appointmentDuration": 20}, {"appointmentId": "101876", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "104706", "patientFirstName": "<PERSON>", "patientLastName": "Michnowska", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "104515", "patientFirstName": "Magdalena", "patientLastName": "Żółtowska", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "105536", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "105577", "patientFirstName": "Małgorzata", "patientLastName": "Olesiak", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "105681", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Gwiazda", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "108315", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "11:50", "appointmentEnd": "12:10", "appointmentDuration": 20}, {"appointmentId": "108295", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "13:20", "appointmentEnd": "13:50", "appointmentDuration": 30}, {"appointmentId": "108318", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "105995", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Muszyńska-Pioruńska", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": "106624", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Aksamitowska-Środoń", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "108190", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "108286", "patientFirstName": "Katarzyna", "patientLastName": "Bylica", "appointmentStart": "18:45", "appointmentEnd": "18:50", "appointmentDuration": 5}, {"appointmentId": "108369", "patientFirstName": "Weronika", "patientLastName": "Sodel", "appointmentStart": "18:50", "appointmentEnd": "18:55", "appointmentDuration": 5}, {"appointmentId": "108384", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Mo<PERSON>ulska", "appointmentStart": "18:55", "appointmentEnd": "19:00", "appointmentDuration": 5}, {"appointmentId": "108419", "patientFirstName": "Magdalena", "patientLastName": "Kalista", "appointmentStart": "19:00", "appointmentEnd": "19:10", "appointmentDuration": 10}]}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": [{"appointmentId": "102981", "patientFirstName": "BLOKADA", "patientLastName": "URLOP", "appointmentStart": "09:00", "appointmentEnd": "14:00", "appointmentDuration": 300}]}, {"doctorId": "83", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "108390", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Cuker", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "108328", "patientFirstName": "Magdalena", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "108293", "patientFirstName": "Katarzyna", "patientLastName": "Czubak", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "108326", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Łoszewska", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "108320", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Wilczyńska", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "105508", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Kobylańska", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "106697", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": "106111", "patientFirstName": "<PERSON>", "patientLastName": "Kowalska", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "108021", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>ak", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": "106605", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "106582", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "20:00", "appointmentEnd": "20:05", "appointmentDuration": 5}, {"appointmentId": "108403", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "20:25", "appointmentEnd": "20:30", "appointmentDuration": 5}, {"appointmentId": "108329", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Mrozińska", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}, {"appointmentId": "107466", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Vovk", "appointmentStart": "19:00", "appointmentEnd": "19:20", "appointmentDuration": 20}, {"appointmentId": "108333", "patientFirstName": "<PERSON>", "patientLastName": "Wojciechowska", "appointmentStart": "19:20", "appointmentEnd": "19:40", "appointmentDuration": 20}, {"appointmentId": "108334", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "19:40", "appointmentEnd": "20:00", "appointmentDuration": 20}]}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "108195", "patientFirstName": "Dominika", "patientLastName": "Stępień", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "107064", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Szczepańska", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "108422", "patientFirstName": "Paul<PERSON>", "patientLastName": "Czech", "appointmentStart": "12:35", "appointmentEnd": "12:55", "appointmentDuration": 20}, {"appointmentId": "107649", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "107931", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON>", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "106686", "patientFirstName": "Weronika", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "13:55", "appointmentEnd": "14:15", "appointmentDuration": 20}, {"appointmentId": "108417", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "14:15", "appointmentEnd": "14:35", "appointmentDuration": 20}, {"appointmentId": "106332", "patientFirstName": "Dominika", "patientLastName": "Kowcza-Wasilewska", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "106478", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "108345", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Kłodzińska", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": "108426", "patientFirstName": "Magdalena", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "108029", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "08:00", "appointmentEnd": "08:15", "appointmentDuration": 15}, {"appointmentId": "108288", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>ak", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}]}]}]}}