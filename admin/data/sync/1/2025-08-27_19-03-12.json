{"exportDate": "2025-08-27T19:03:12.258Z", "syncCode": "igab1234567890123", "syncData": {"days": [{"date": "2025-08-27", "doctors": [{"doctorId": "**********", "doctorName": "dermatolog dr <PERSON>-Wójcik", "appointments": []}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "108317", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Cioch-Bogusz", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "107602", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Ziarkiewicz- Mormul", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "108516", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "107049", "patientFirstName": "<PERSON>", "patientLastName": "Dąbrowska", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}]}, {"doctorId": "43", "doctorName": "genetyk dr n.med. <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "105", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "114", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "108107", "patientFirstName": "Natalia", "patientLastName": "Kowalska", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "107816", "patientFirstName": "Katarzyna", "patientLastName": "Dworak", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "107750", "patientFirstName": "Paul<PERSON>", "patientLastName": "Szczygielska", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "108401", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Zagólska", "appointmentStart": "14:00", "appointmentEnd": "14:20", "appointmentDuration": 20}, {"appointmentId": "107730", "patientFirstName": "Daria", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "14:20", "appointmentEnd": "14:40", "appointmentDuration": 20}, {"appointmentId": "107557", "patientFirstName": "Paul<PERSON>", "patientLastName": "Pniewska", "appointmentStart": "14:40", "appointmentEnd": "15:00", "appointmentDuration": 20}, {"appointmentId": "108363", "patientFirstName": "Nikola", "patientLastName": "Wiśniewska", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "108400", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Melnyk", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "104670", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Wisełka", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "108398", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "104234", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "104138", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": "105900", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Paliszek-Saładyga", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "106223", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Piątkowska", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}, {"appointmentId": "108404", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "12:00", "appointmentEnd": "12:40", "appointmentDuration": 40}, {"appointmentId": "107743", "patientFirstName": "Paul<PERSON>", "patientLastName": "Saktura", "appointmentStart": "13:40", "appointmentEnd": "14:00", "appointmentDuration": 20}, {"appointmentId": "103604", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "103614", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Galews<PERSON>", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "105149", "patientFirstName": "Magdalena", "patientLastName": "Ciesielska", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "104628", "patientFirstName": "Paul<PERSON>", "patientLastName": "E<PERSON><PERSON>", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}]}, {"doctorId": "129", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "102554", "patientFirstName": "BLOKADA", "patientLastName": "URLOP", "appointmentStart": "15:00", "appointmentEnd": "19:00", "appointmentDuration": 240}]}, {"doctorId": "72", "doctorName": "ginekolog dr <PERSON>-<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "104349", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "14:00", "appointmentEnd": "14:20", "appointmentDuration": 20}, {"appointmentId": "108431", "patientFirstName": "Nikola", "patientLastName": "Browarny", "appointmentStart": "14:20", "appointmentEnd": "14:40", "appointmentDuration": 20}, {"appointmentId": "104702", "patientFirstName": "Paul<PERSON>", "patientLastName": "Nowotarska", "appointmentStart": "14:40", "appointmentEnd": "15:00", "appointmentDuration": 20}, {"appointmentId": "104756", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:00", "appointmentEnd": "15:20", "appointmentDuration": 20}, {"appointmentId": "107662", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "15:20", "appointmentEnd": "15:40", "appointmentDuration": 20}, {"appointmentId": "108267", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Mrówczyńska", "appointmentStart": "15:40", "appointmentEnd": "16:00", "appointmentDuration": 20}, {"appointmentId": "104841", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:00", "appointmentEnd": "16:20", "appointmentDuration": 20}, {"appointmentId": "108394", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "16:20", "appointmentEnd": "16:40", "appointmentDuration": 20}, {"appointmentId": "104966", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Tylikowska", "appointmentStart": "16:40", "appointmentEnd": "17:00", "appointmentDuration": 20}, {"appointmentId": "108175", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentEnd": "17:20", "appointmentDuration": 20}, {"appointmentId": "108525", "patientFirstName": "Magdalena", "patientLastName": "Wielgusz", "appointmentStart": "17:20", "appointmentEnd": "17:40", "appointmentDuration": 20}, {"appointmentId": "108527", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Błońska", "appointmentStart": "17:40", "appointmentEnd": "18:00", "appointmentDuration": 20}, {"appointmentId": "104648", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Grzesińska", "appointmentStart": "18:00", "appointmentEnd": "18:20", "appointmentDuration": 20}, {"appointmentId": "108432", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "18:20", "appointmentEnd": "18:40", "appointmentDuration": 20}, {"appointmentId": "108444", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Pelińska", "appointmentStart": "18:40", "appointmentEnd": "19:00", "appointmentDuration": 20}, {"appointmentId": "108524", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "19:20", "appointmentEnd": "19:25", "appointmentDuration": 5}]}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": []}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": [{"appointmentId": "103547", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Liszewska", "appointmentStart": "09:40", "appointmentEnd": "10:00", "appointmentDuration": 20}, {"appointmentId": "108395", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Łękacka", "appointmentStart": "10:00", "appointmentEnd": "10:20", "appointmentDuration": 20}, {"appointmentId": "103531", "patientFirstName": "Agata", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "11:00", "appointmentEnd": "11:20", "appointmentDuration": 20}, {"appointmentId": "103406", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>wicz-W<PERSON><PERSON>rek", "appointmentStart": "11:20", "appointmentEnd": "11:40", "appointmentDuration": 20}, {"appointmentId": "103303", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "12:00", "appointmentEnd": "12:20", "appointmentDuration": 20}, {"appointmentId": "108439", "patientFirstName": "Svitlana", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:40", "appointmentEnd": "13:00", "appointmentDuration": 20}, {"appointmentId": "104584", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "09:00", "appointmentEnd": "09:20", "appointmentDuration": 20}, {"appointmentId": "103559", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "09:20", "appointmentEnd": "09:40", "appointmentDuration": 20}, {"appointmentId": "104610", "patientFirstName": "Paul<PERSON>", "patientLastName": "Świerczyńska", "appointmentStart": "10:20", "appointmentEnd": "10:40", "appointmentDuration": 20}, {"appointmentId": "104501", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Zawadzka", "appointmentStart": "10:40", "appointmentEnd": "11:00", "appointmentDuration": 20}, {"appointmentId": "106259", "patientFirstName": "Daria", "patientLastName": "<PERSON><PERSON>z<PERSON>", "appointmentStart": "11:40", "appointmentEnd": "12:00", "appointmentDuration": 20}, {"appointmentId": "104183", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "Roman", "appointmentStart": "12:20", "appointmentEnd": "12:40", "appointmentDuration": 20}, {"appointmentId": "104408", "patientFirstName": "Paul<PERSON>", "patientLastName": "Gondek", "appointmentStart": "13:00", "appointmentEnd": "13:20", "appointmentDuration": 20}, {"appointmentId": "104537", "patientFirstName": "<PERSON>", "patientLastName": "Karsznia", "appointmentStart": "13:20", "appointmentEnd": "13:40", "appointmentDuration": 20}, {"appointmentId": "108399", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "pacjent", "appointmentStart": "13:40", "appointmentEnd": "14:00", "appointmentDuration": 20}, {"appointmentId": "108428", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "14:35", "appointmentEnd": "14:40", "appointmentDuration": 5}, {"appointmentId": "108473", "patientFirstName": "Agata", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "14:40", "appointmentEnd": "14:45", "appointmentDuration": 5}]}, {"doctorId": "83", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "19", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "82", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "110", "doctorName": "ginekolog dziecięcy dr Agnieszka Tyszko-Tymińska", "appointments": []}, {"doctorId": "**********", "doctorName": "hepa<PERSON><PERSON>, specjalista chorób zakaźnych dr <PERSON><PERSON>", "appointments": []}]}]}}